const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  output: 'standalone',
  basePath: process.env.NEXT_PUBLIC_BASE_PATH,
  assetPrefix: process.env.NEXT_PUBLIC_BASE_PATH,
  productionBrowserSourceMaps: true,
  publicRuntimeConfig: {
    basePath: process.env.NEXT_PUBLIC_BASE_PATH,
    assetPrefix: process.env.NEXT_PUBLIC_BASE_PATH,
  },
  env: {
    NEXT_PUBLIC_MY_COMPENSATION_URL:
      process.env.NEXT_PUBLIC_MY_COMPENSATION_URL,
    NEXT_PUBLIC_MY_TALENT_CARD_URL: process.env.NEXT_PUBLIC_MY_TALENT_CARD_URL,
    NEXT_PUBLIC_LCM_URL: process.env.NEXT_PUBLIC_LCM_URL,
    NEXT_PUBLIC_CHEERSHUB_URL: process.env.NEXT_PUBLIC_CHEERSHUB_URL,
    NEXT_PUBLIC_WORKDAY_URL: process.env.NEXT_PUBLIC_WORKDAY_URL,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    PUBLIC_PP_AUTH_LIB_AZURE_REDIRECT_URI:
      process.env.PUBLIC_PP_AUTH_LIB_AZURE_REDIRECT_URI,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NEXT_PUBLIC_BASE_PATH: process.env.NEXT_PUBLIC_BASE_PATH,
    PP_AUTH_LIB_REDIS_URL: process.env.PP_AUTH_LIB_REDIS_URL,
    PP_AUTH_LIB_AZURE_CLIENT_ID: process.env.PP_AUTH_LIB_AZURE_CLIENT_ID,
    PP_AUTH_LIB_AZURE_CLIENT_SECRET:
      process.env.PP_AUTH_LIB_AZURE_CLIENT_SECRET,
    NEXT_PUBLIC_PP_AUTH_LIB_AZURE_TENANT_ID:
      process.env.NEXT_PUBLIC_PP_AUTH_LIB_AZURE_TENANT_ID,
    PP_AUTH_LIB_AZURE_DEBUG: process.env.PP_AUTH_LIB_AZURE_DEBUG,
    PP_AUTH_LIB_AZURE_HTTP_TIMEOUT: process.env.PP_AUTH_LIB_AZURE_HTTP_TIMEOUT,
    PP_AUTH_LIB_SYSTEM_ENUM: process.env.PP_AUTH_LIB_SYSTEM_ENUM,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    NEXT_PUBLIC_NORTHSTAR_AUTH_API_URL:
      process.env.NEXT_PUBLIC_NORTHSTAR_AUTH_API_URL,
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_AUTH_API_URL: process.env.NEXT_PUBLIC_AUTH_API_URL,
    NEXT_PUBLIC_AUTH_API_URL_INTERNAL:
      process.env.NEXT_PUBLIC_AUTH_API_URL_INTERNAL,
    NEXT_PUBLIC_PEOPLE_PLATFORM_API_URL:
      process.env.NEXT_PUBLIC_PEOPLE_PLATFORM_API_URL,
    NEXT_PUBLIC_PEOPLE_PLATFORM_PHOTO_API_URL:
      process.env.NEXT_PUBLIC_PEOPLE_PLATFORM_PHOTO_API_URL,
    NEXT_PUBLIC_TOLGEE_CONTENT_DELIVERY_URL:
      process.env.NEXT_PUBLIC_TOLGEE_CONTENT_DELIVERY_URL,
    NEXT_PUBLIC_TOLGEE_API_URL: process.env.NEXT_PUBLIC_TOLGEE_API_URL,
    NEXT_PUBLIC_TOLGEE_API_KEY: process.env.NEXT_PUBLIC_TOLGEE_API_KEY,
    NEXT_PUBLIC_NORTHSTAR_USER_API_URL:
      process.env.NEXT_PUBLIC_NORTHSTAR_USER_API_URL,
    NEXT_PUBLIC_GA_ID: process.env.NEXT_PUBLIC_GA_ID,
  },
  i18n: {
    locales: ['en-US'],
    defaultLocale: 'en-US',
  },
  webpack(config) {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      dns: false,
      net: false,
      tls: false,
    };

    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      react: path.resolve(__dirname, 'node_modules/react'),
      'react-dom': path.resolve(__dirname, 'node_modules/react-dom'),
    };

    return config;
  },

  async headers() {
    return [
      {
        source: `${process.env.NEXT_PUBLIC_BASE_PATH}/fonts/:font*`,
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, immutable, max-age=31536000',
          },
        ],
      },
    ];
  },

  async rewrites() {
    return [
      {
        source: `${process.env.NEXT_PUBLIC_BASE_PATH}/_next/:path*`,
        destination: '/_next/:path*',
      },
    ];
  },
};

module.exports = nextConfig;
