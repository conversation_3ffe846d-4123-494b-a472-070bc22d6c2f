import type {
  GetServerSideProps,
  GetServerSidePropsContext,
  GetServerSidePropsResult,
} from 'next';
import { getServerSession } from 'next-auth/next';
import { nextAuthOptions } from '@ghq-abi/auth-client-lib';

export function withSSRUnauth<T extends Record<string, unknown>>(
  fn?: GetServerSideProps<T>,
) {
  return async (
    context: GetServerSidePropsContext,
  ): Promise<GetServerSidePropsResult<T>> => {
    const session = await getServerSession(
      context.req,
      context.res,
      nextAuthOptions,
    );

    if (session) {
      return {
        redirect: { permanent: false, destination: '/' },
      };
    }

    if (!fn) {
      return {
        props: {} as T,
      };
    }

    return fn(context);
  };
}
