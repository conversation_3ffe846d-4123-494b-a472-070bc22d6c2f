import type { GetServerSidePropsContext, GetServerSidePropsResult } from 'next';
import { getServerSession } from 'next-auth';
import { JWT } from 'next-auth/jwt';
import { AuthCache, nextAuthOptions } from '@ghq-abi/auth-client-lib';

import { type Flags } from '../contexts/FlagsContext';

function checkIfIsMobileDevice(userAgent: string) {
  return /iPhone|iPad|iPod|Android/i.test(userAgent);
}

export function withSSRSession<T extends Record<string, unknown>>(
  fn?: (
    context: GetServerSidePropsContext,
    session: JWT | null,
  ) => Promise<GetServerSidePropsResult<T>>,
) {
  return async <
    Q extends {
      session: JWT | null;
      isMobile: boolean;
      flags: Flags;
    } & T,
  >(
    context: GetServerSidePropsContext,
  ): Promise<GetServerSidePropsResult<Q>> => {
    const session = await getServerSession(
      context.req,
      context.res,
      nextAuthOptions,
    );

    const userSession = await AuthCache.getUserSession(
      session?.user.employeeId,
    );
    if (userSession?.user) {
      delete userSession?.user?.token;
      delete userSession?.user?.refreshToken;
      delete userSession?.user?.proxiedAs?.token;
      delete userSession?.user?.proxiedAs?.refreshToken;
    }

    const isMobile = checkIfIsMobileDevice(
      context.req.headers['user-agent'] ?? '',
    );

    if (!fn) {
      return {
        props: {
          session: userSession,
          isMobile,
        } as Q,
      };
    }

    const result = await fn(context, userSession);

    if ('props' in result) {
      return {
        ...result,
        props: {
          ...result.props,
          session: userSession,
          isMobile,
        } as unknown as Q,
      };
    }

    return result;
  };
}
