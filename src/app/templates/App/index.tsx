import React from 'react';
import { useSearchParams } from 'next/navigation';
import { AuthService } from '@ghq-abi/auth-client-lib';
import { Box, useResponsive } from '@ghq-abi/design-system';
import axios from 'axios';

import { DesktopMenu, <PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>, Header } from '~/app/components';
import { useStellarWidget } from '~/shared/hooks/useStellarWidget';
import { loginProxyUrl, loginSSO } from '~/shared/services/auth';

import { StyledContent, StyledWrapper } from './styles';

type AppTemplateProps = {
  children: React.ReactNode;
};

export function AppTemplate({ children }: AppTemplateProps) {
  const [isLoggedIn, setIsLoggedIn] = React.useState(false);
  useStellarWidget();

  const searchParams = useSearchParams();

  const getCSRFTokenFromCookies = () => {
    const cookie: { [key: string]: string } = { _csrf: '' };
    document.cookie.split(';').forEach(function (el) {
      const [k, v] = el.split('=');
      cookie[k.trim()] = v;
    });
    /* eslint-disable no-underscore-dangle */
    return cookie._csrf;
  };

  const handleNorthstarAuth = async () => {
    const session = await AuthService.getCachedUserTokens();

    axios.defaults.headers.common['Authorization'] = `Bearer ${session?.token}`;

    const { str_email, str_token } = {
      str_email: searchParams?.get('email'),
      str_token: searchParams?.get('token'),
    };
    let promise;
    if (str_email && str_token) {
      promise = loginProxyUrl(str_email, str_token);
    } else {
      promise = loginSSO();
    }
    const response = await promise;

    localStorage.setItem('user', JSON.stringify(response.data.results[0]));

    axios.defaults.headers.Authorization = `Bearer ${response.data.results[0].accessToken}`;
    axios.defaults.headers['X-CSRF-Token'] = getCSRFTokenFromCookies();

    setIsLoggedIn(true);
  };

  React.useEffect(() => {
    void handleNorthstarAuth();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { isMobile } = useResponsive({ useOnlyAbiTokens: true });

  return (
    <>
      <Header />

      <StyledWrapper>
        {!isMobile && <DesktopMenu />}

        <StyledContent as="main">
          <ErrorBoundary inApp>
            <Box css={{ flex: 1 }}>
              {isLoggedIn ? (
                children
              ) : (
                <Box
                  css={{
                    flex: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <p>Loading...</p>
                </Box>
              )}
            </Box>
          </ErrorBoundary>
        </StyledContent>
      </StyledWrapper>
    </>
  );
}
