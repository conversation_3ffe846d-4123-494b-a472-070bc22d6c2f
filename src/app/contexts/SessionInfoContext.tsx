import * as React from 'react';
import { Session } from 'next-auth';
import { AuthenticatedUser } from '@ghq-abi/auth-client-lib';
import { FormatIcu } from '@tolgee/format-icu';
import {
  BackendFetch,
  DevTools,
  Tolgee,
  TolgeeProvider,
  useTolgeeSSR,
} from '@tolgee/react';

import { DEFAULT_LANGUAGE } from '~/shared/constants/i18n';

type UserSessionCustomData = {
  language: string;
};

export type CommonUserData = {
  globalId: string;
  name: string;
  zone: string;
  band: string;
  employeeId: string;
  token: string;
  refreshToken: string;
  homeLocation: string;
} & UserSessionCustomData;

export type BoilerplateSession = {
  country: string;
  email: string;
  proxiedAs?: CommonUserData | null;
} & AuthenticatedUser &
  CommonUserData;

export type BoilerplateCustomSessionData = {
  sessionInfo: BoilerplateSession;
  updateCurrentLanguage?: (newLanguage: string) => void;
};

type AuthProviderProps = {
  children: React.ReactNode;
  sessionInfo: Session | undefined;
};

const SessionInfoContext = React.createContext<BoilerplateCustomSessionData>(
  {} as BoilerplateCustomSessionData,
);

export const preferredLanguage =
  (typeof window !== 'undefined' &&
    (localStorage.getItem('currentLanguage') ||
      JSON.parse(localStorage.getItem('user') ?? '{}')
        .str_preferredlanguage)) ??
  DEFAULT_LANGUAGE;

export const tolgee = Tolgee()
  .use(
    BackendFetch({
      prefix: process.env.NEXT_PUBLIC_TOLGEE_CONTENT_DELIVERY_URL,
    }),
  )
  .use(DevTools())
  .use(FormatIcu())
  .init({
    language: preferredLanguage,
    defaultLanguage: preferredLanguage,
    apiKey: process.env.NEXT_PUBLIC_TOLGEE_API_KEY,
    apiUrl: process.env.NEXT_PUBLIC_TOLGEE_API_URL,
  });

export function SessionInfoProvider(props: AuthProviderProps) {
  const { children } = props;

  const [providerValue, setProviderValue] =
    React.useState<BoilerplateCustomSessionData>(() => {
      const { sessionInfo } = props;
      if (!sessionInfo) {
        return {} as BoilerplateCustomSessionData;
      }

      const boilerplateCustomSession: BoilerplateCustomSessionData = {
        sessionInfo: {
          globalId: sessionInfo.user.globalId,
          employeeId: sessionInfo.user.employeeId,
          name: sessionInfo.user.name,
          zone: sessionInfo.user.zone,
          band: sessionInfo.user.band,
          country: sessionInfo.user.country ?? '',
          email: sessionInfo.user.email,
          language: preferredLanguage ?? sessionInfo.user.language,
          refreshToken: sessionInfo.user.refreshToken,
          token: sessionInfo.user.token,
          proxiedAs: null,
          homeLocation: sessionInfo.user.homeLocation,
          roles: sessionInfo.user.roles,
        },
      };

      if (sessionInfo.user.proxiedAs) {
        boilerplateCustomSession.sessionInfo.proxiedAs = {
          language: preferredLanguage ?? sessionInfo.user.proxiedAs.language,
          band: sessionInfo.user.proxiedAs.band,
          country: sessionInfo.user.proxiedAs.country ?? '',
          employeeId: sessionInfo.user.proxiedAs.employeeId,
          globalId: sessionInfo.user.proxiedAs.globalId,
          name: sessionInfo.user.proxiedAs.name,
          token: sessionInfo.user.proxiedAs.token,
          refreshToken: sessionInfo.user.proxiedAs.refreshToken,
          zone: sessionInfo.user.proxiedAs.zone,
          homeLocation: sessionInfo.user.proxiedAs.homeLocation,
          roles: sessionInfo.user.proxiedAs.roles,
        };
      }

      return boilerplateCustomSession;
    });

  const locale =
    preferredLanguage ??
    providerValue.sessionInfo?.proxiedAs?.language ??
    providerValue.sessionInfo?.language;

  const ssrTolgee = useTolgeeSSR(tolgee, locale);

  React.useEffect(() => {
    void tolgee.changeLanguage(locale);
  }, [locale]);

  const updateCurrentLanguage = React.useCallback((newLanguage: string) => {
    setProviderValue(prevState => {
      const updatedSessionInfo = { ...prevState.sessionInfo };

      if (updatedSessionInfo.proxiedAs) {
        updatedSessionInfo.proxiedAs.language = newLanguage;
      } else {
        updatedSessionInfo.language = newLanguage;
      }

      if (typeof window !== 'undefined') {
        localStorage.setItem('currentLanguage', newLanguage);
        const user = JSON.parse(localStorage.getItem('user') ?? '{}');
        user.str_preferredlanguage = newLanguage;
        localStorage.setItem('user', JSON.stringify(user));
      }
      return { ...prevState, sessionInfo: updatedSessionInfo };
    });

    void tolgee.changeLanguage(newLanguage);
  }, []);

  const memoizedValue = {
    sessionInfo: providerValue.sessionInfo,
    updateCurrentLanguage,
  };
  return (
    <SessionInfoContext.Provider value={memoizedValue}>
      <TolgeeProvider tolgee={ssrTolgee}>{children}</TolgeeProvider>
    </SessionInfoContext.Provider>
  );
}

export function useSessionInfo() {
  const context = React.useContext(SessionInfoContext);

  if (!context) {
    throw new Error(
      'useSessionInfo must be used within an SessionInfoProvider',
    );
  }

  return { ...context, preferredLanguage };
}
