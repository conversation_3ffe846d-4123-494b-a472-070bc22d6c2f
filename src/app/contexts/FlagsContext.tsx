import * as React from 'react';

import { pagesPaths, PagesPathsKeys } from '~/shared/constants/pages';

export type Actions = 'massSelect';

export type Flags = Record<
  PagesPathsKeys | Actions,
  { active: boolean; features?: Record<string, boolean> }
>;

type FlagsContextData = {
  flags: Flags;
  skipCheck: boolean;
};

const FlagsContext = React.createContext({} as FlagsContextData);

type FlagsProviderProps = {
  children: React.ReactNode;
  flags?: Partial<Flags>;
  skipCheck?: boolean;
};

export function FlagsProvider({
  children,
  flags,
  skipCheck = false,
}: FlagsProviderProps) {
  const providerValue = React.useMemo(
    () => ({
      flags: {
        ...Object.keys(pagesPaths).reduce(
          (acc, curr) => ({ ...acc, [curr]: { active: true } }),
          {} as Flags,
        ),
        ...flags,
      },
      skipCheck,
    }),
    [flags, skipCheck],
  );

  return (
    <FlagsContext.Provider value={providerValue}>
      {children}
    </FlagsContext.Provider>
  );
}

export function useFlags() {
  const context = React.useContext(FlagsContext);

  if (!context) {
    throw new Error('useFlags must be used within a FlagsProvider');
  }

  return context.flags;
}

type MountFlagsOptions = {
  skipCheck?: boolean;
};

export function mountFlags(options: MountFlagsOptions = {}): Flags {
  if (options.skipCheck) {
    return Object.keys(pagesPaths).reduce(
      (acc, curr) => ({
        ...acc,
        [curr]: { active: true },
      }),
      {},
    ) as Flags;
  }

  return {
    new_kpi_catalog: {
      active: process.env.PAGE_HOME_ENABLED !== 'false',
    },
    proxy: {
      active: process.env.PAGE_PROXY_ENABLED !== 'false',
    },
  } as Flags;
}

type ShouldBlockAccessOptions = {
  skipCheck?: boolean;
};

export function shouldBlockAccess(
  path: string,
  options: ShouldBlockAccessOptions = {},
) {
  if (options.skipCheck) {
    return false;
  }

  const flags = mountFlags({ skipCheck: options.skipCheck });

  // eslint-disable-next-line no-restricted-syntax
  for (const [key, value] of Object.entries(pagesPaths)) {
    if (typeof value === 'string' && path === value) {
      return !flags[key as PagesPathsKeys].active;
    }

    // if (value instanceof RegExp && value.test(path)) {
    //   return !flags[key as PagesPathsKeys].active;
    // }
  }

  return false;
}
