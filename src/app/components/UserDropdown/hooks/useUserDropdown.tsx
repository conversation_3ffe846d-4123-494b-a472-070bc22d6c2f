import { useCallback, useMemo } from 'react';
import { useRouter } from 'next/router';
import { signIn, signOut, useSession } from 'next-auth/react';
import {
  AuthenticatedUser,
  CredentialProvidersEnum,
} from '@ghq-abi/auth-client-lib';
import { useTranslate } from '@tolgee/react';
import nprogress from 'nprogress';

import { useAbility } from '~/app/contexts/AbilityContext';
import { useSessionInfo } from '~/app/contexts/SessionInfoContext';
import { useGetProfilePicture } from '~/entities/Employee/hooks/useGetProfilePicture';
import { Icon } from '~/shared/components';
import { SignOut } from '~/shared/components/icons/SignOut';

type UseUserDropdownProps = {
  user: any;
};

export const useUserDropdown = ({ user }: UseUserDropdownProps) => {
  const { t } = useTranslate(['default']);
  const nextAuthSession = useSession();
  const { sessionInfo } = useSessionInfo();
  const router = useRouter();
  const ability = useAbility();
  const basePath = process.env.NEXT_PUBLIC_BASE_PATH;
  const isProxying = !!sessionInfo?.proxiedAs;

  async function handleSignOut() {
    signOut({ redirect: false });
  }

  const goToProxy = useCallback(async () => {
    router.push('/proxy');
  }, [router]);

  const handleStopProxy = useCallback(async () => {
    if (!nextAuthSession) {
      return;
    }

    nprogress.start();

    const userObject = JSON.stringify({
      ...sessionInfo,
      proxiedAs: null,
    } as AuthenticatedUser);
    await signIn(CredentialProvidersEnum.REGISTER_PROXY, {
      user: userObject,
      callbackUrl: `${basePath}/`,
    });
    nprogress.done();
  }, [nextAuthSession, sessionInfo]);

  const items = useMemo(() => {
    const dropdownItems = [
      {
        label: t('common.sign_out'),
        onSelect: handleSignOut,
        icon: <SignOut />,
      },
    ];

    if (ability.can('proxy', 'User')) {
      dropdownItems.unshift({
        label: t(isProxying ? 'common.stop_proxy' : 'common.proxy_as_user'),
        onSelect: isProxying ? handleStopProxy : goToProxy,
        icon: isProxying ? (
          <Icon.StopProxy size={18} />
        ) : (
          <Icon.Proxy size={18} />
        ),
      });
    }

    return dropdownItems;
  }, [ability, goToProxy, handleStopProxy, isProxying, t]);

  const northstarUser = JSON.parse(localStorage.getItem('user') ?? '{}');
  const { data } = useGetProfilePicture(
    user.globalId ?? northstarUser.int_employeeglobalid ?? '',
  );
  const imageSrc = data ? URL.createObjectURL(data) : undefined;

  const profileInfo = {
    name: user.name ?? '',
    urlImage: imageSrc,
    zone: user.zone ?? '',
  };

  return {
    items,
    profileInfo,
  };
};
