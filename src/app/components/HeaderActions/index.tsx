import * as React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { Box, Flex, Text, useResponsive } from '@ghq-abi/design-system';
import { useTranslate } from '@tolgee/react';

import { useAbility } from '~/app/contexts/AbilityContext';

import oprLogo from '~/../public/img/abi_logo.png';

import { MobileMenu } from '../Nav/components/MobileMenu';

import { StyledActionWrapper } from './styles';

type HomeLinkProps = {
  children: React.ReactNode;
};

function HomeLink({ children }: HomeLinkProps) {
  const router = useRouter();
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || '';

  const proxyUrl =
    window.location.hash === '#login-url'
      ? `?email=${router.query.email}&token=${router.query.token}#login-url`
      : '';

  return (
    <Link href={baseUrl + proxyUrl} legacyBehavior passHref>
      {children}
    </Link>
  );
}

export function HeaderActions() {
  const { t } = useTranslate(['menu', 'default']);
  const { isMobile } = useResponsive({ useOnlyAbiTokens: true });

  const ability = useAbility();

  const LogoContainer = ability.can('view', 'Home') ? HomeLink : React.Fragment;

  return (
    <StyledActionWrapper>
      {isMobile && <MobileMenu />}
      <LogoContainer>
        <Flex
          align="center"
          gap="sm"
          as="a"
          title={t('common.navigate_to_home', { ns: 'default' })}
          css={{ textDecoration: 'none' }}
        >
          <Text
            css={{
              fontSize: '$2xl',
              fontWeight: '$bold',
              background:
                'linear-gradient(90deg, #D3A238 3.33%, #F5E003 40.91%)',
              backgroundClip: 'text',
              textFillColor: 'transparent',
              '-webkit-background-clip': 'text',
              '-webkit-text-fill-color': 'transparent',
            }}
          >
            NORTHSTAR
          </Text>
          <Box
            css={{
              display: 'none',
              '@lg': { display: 'block' },
              fontSize: 0,
            }}
          >
            <Image src={oprLogo} alt="Ab InBev" placeholder="blur" />
          </Box>
        </Flex>
      </LogoContainer>
    </StyledActionWrapper>
  );
}
