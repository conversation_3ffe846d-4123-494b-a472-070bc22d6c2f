import '@ghq-abi/design-system/css';
import '@ghq-abi/design-system-v2/css';

import * as React from 'react';
import { SessionProvider, signIn } from 'next-auth/react';
import NextNProgress from 'nextjs-progressbar';
import { CredentialProvidersEnum } from '@ghq-abi/auth-client-lib';
import { theme } from '@ghq-abi/design-system';
import { ThemeProvider } from '@ghq-abi/design-system-v2';
import {
  Hydrate,
  QueryClient,
  QueryClientProvider,
} from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { TolgeeProvider, useTolgeeSSR } from '@tolgee/react';

import { ErrorBoundary } from '~/app/components';
import { AbilityProvider } from '~/app/contexts/AbilityContext';
import { FlagsProvider } from '~/app/contexts/FlagsContext';
import { NavProvider } from '~/app/contexts/NavContext';
import {
  preferredLanguage,
  SessionInfoProvider,
  tolgee,
} from '~/app/contexts/SessionInfoContext';
import { ToastProvider } from '~/app/contexts/ToastContext';
import { globalStyles } from '~/app/styles/global';
import { AppTemplate } from '~/app/templates/App';
import { CustomAppProps } from '~/app/types';
import { createUserAbility } from '~/shared/auth/permissions';
import { queryClientConfig, useDatadog } from '~/shared/lib';
import { isDevelopment, isLocal } from '~/shared/utils/environments';

import './globals.css';

globalStyles();

export default function App({
  Component,
  pageProps: { session, messages, abilityRules, isMobile, flags, ...pageProps },
}: CustomAppProps) {
  // https://tanstack.com/query/v4/docs/react/guides/ssr#using-hydration
  const [queryClient] = React.useState(
    () => new QueryClient(queryClientConfig),
  );
  const basePath = process.env.NEXT_PUBLIC_BASE_PATH;
  const locale =
    preferredLanguage || session?.user?.preferredLanguage || 'en-US';
  const ssrTolgee = useTolgeeSSR(tolgee, locale);

  useDatadog(locale, session);

  const handleOldCookiesReq = async () => {
    const res = await fetch(`${basePath}/api/auth/old-cookies-handler`);

    if (res.status === 200) {
      return;
    }

    signIn(CredentialProvidersEnum.AZURE_AD, { callbackUrl: `${basePath}/` });
  };

  const shouldSignInReq = async () => {
    const res = await fetch(`${basePath}/api/auth/should-sign-in`);

    if (res.status === 200) {
      return;
    }

    signIn(CredentialProvidersEnum.AZURE_AD, { callbackUrl: `${basePath}/` });
  };

  React.useEffect(() => {
    handleOldCookiesReq();
    shouldSignInReq();
  }, []);

  return (
    <TolgeeProvider tolgee={ssrTolgee}>
      <ErrorBoundary>
        <SessionProvider session={session} basePath={`${basePath}/api/auth`}>
          <SessionInfoProvider sessionInfo={session}>
            <QueryClientProvider client={queryClient}>
              <ThemeProvider>
                {Component.unauth ? (
                  <Component {...pageProps} />
                ) : (
                  <ToastProvider>
                    <AbilityProvider
                      ability={createUserAbility(session?.user, abilityRules)}
                    >
                      <FlagsProvider flags={flags}>
                        <NavProvider>
                          <Hydrate state={pageProps.dehydratedState}>
                            <AppTemplate>
                              <Component {...pageProps} />
                            </AppTemplate>
                          </Hydrate>
                          <NextNProgress
                            color={theme.colors.black.toString()}
                            startPosition={0.3}
                            stopDelayMs={200}
                            height={3}
                          />
                        </NavProvider>
                      </FlagsProvider>
                    </AbilityProvider>
                  </ToastProvider>
                )}
              </ThemeProvider>
              {(isDevelopment() || isLocal()) && <ReactQueryDevtools />}
            </QueryClientProvider>
          </SessionInfoProvider>
        </SessionProvider>
      </ErrorBoundary>
    </TolgeeProvider>
  );
}
