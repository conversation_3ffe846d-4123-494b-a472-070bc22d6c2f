import { GetServerSidePropsContext } from 'next';

import { withSSRSession } from '~/app/hocs';
import * as DeliverableDetails from '~/entities/DeliverableDetails';

interface DeliverableDetailsIndexProps {
  deliverableUid: string;
}

export default function DeliverableDetailsIndex({
  deliverableUid,
}: DeliverableDetailsIndexProps) {
  return <DeliverableDetails.Page deliverableUid={deliverableUid} />;
}

export const getServerSideProps = withSSRSession(
  async (ctx: GetServerSidePropsContext) => {
    const deliverableUid = ctx.query.id as string;

    return {
      props: {
        deliverableUid,
      },
    };
  },
);
