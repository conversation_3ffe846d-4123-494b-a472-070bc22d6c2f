import { NextApiRequest, NextApiResponse } from 'next';
import NextAuth from 'next-auth';
import {
  nextAuthOptions,
  setCustomSessionDataHandler,
} from '@ghq-abi/auth-client-lib';

setCustomSessionDataHandler(async session => {
  const customSession = {
    ...session,
    roles: { isGlobalAdmin: true },
  };

  if (!!session.proxiedAs) {
    customSession.proxiedAs = {
      ...session.proxiedAs,
      roles: { isGlobalAdmin: true },
    };
  }

  return customSession;
});

export default async function auth(req: NextApiRequest, res: NextApiResponse) {
  return NextAuth(req, res, nextAuthOptions);
}
