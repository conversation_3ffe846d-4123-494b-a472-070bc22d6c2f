import Head from 'next/head';
import { useTranslate } from '@tolgee/react';

import { withSSRSession } from '~/app/hocs';
import { ContentPage } from '~/app/templates/ContentPage';
import { Search } from '~/entities/Proxy';
import { createUserAbility } from '~/shared/auth/permissions';

export default function ProxyIndex() {
  const { t } = useTranslate('proxy');

  return (
    <>
      <Head>
        <title>{t('proxy.title')}</title>
      </Head>

      <ContentPage title={t('proxy.title')}>
        <Search.ProxyAsUser />
      </ContentPage>
    </>
  );
}

export const getServerSideProps = withSSRSession(async (_, session) => {
  const ability = createUserAbility(session?.user);

  if (ability.cannot('proxy', 'User')) {
    return {
      redirect: {
        destination: '/',
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
});
