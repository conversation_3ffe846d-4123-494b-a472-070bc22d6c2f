import { AiOutlineArrowUp } from 'react-icons/ai';
import Head from 'next/head';
import Link from 'next/link';
import { Text } from '@ghq-abi/design-system';
import { useTranslate } from '@tolgee/react';

import { AuthErrorTemplate } from '~/app/templates/AuthError';
import { Can, Flags } from '~/shared/components';

export default function NotFoundPage() {
  const { t } = useTranslate('default');

  return (
    <>
      <Head>
        <title>{`404 | ${t('error_pages.404.title')}`}</title>
      </Head>

      <AuthErrorTemplate>
        <Text css={{ lineHeight: '$shorter', fontSize: '$2', mb: '$sm' }}>
          404 | {t('error_pages.404.message')}
        </Text>

        <Flags authorizedFlags="new_kpi_catalog">
          <Can I="view" a="Home">
            {() => (
              <Link href="/">
                <Text
                  as="span"
                  css={{
                    color: '#0053ad',
                    fontWeight: '$medium',
                    fontSize: '$xs',
                    borderBottom: '1px solid #0053ad',
                  }}
                >
                  <AiOutlineArrowUp style={{ transform: 'rotate(-45deg)' }} />{' '}
                  {t('error_pages.404.go_back_to_home_page')}
                </Text>
              </Link>
            )}
          </Can>
        </Flags>
      </AuthErrorTemplate>
    </>
  );
}
