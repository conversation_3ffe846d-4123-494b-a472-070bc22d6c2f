import * as DeliverableForm from '~/entities/DeliverableForm';
import { GlobalAdminProtectedRoute } from '~/shared/components/GlobalAdminProtectedRoute';
import { withAdminRoute } from '~/shared/hocs/withAdminRoute';

export default function DeliverableCreateIndex() {
  return (
    <GlobalAdminProtectedRoute>
      <DeliverableForm.Page />
    </GlobalAdminProtectedRoute>
  );
}

export const getServerSideProps = withAdminRoute();
