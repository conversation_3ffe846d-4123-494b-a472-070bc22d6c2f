import { useRouter } from 'next/router';

import * as DeliverableForm from '~/entities/DeliverableForm';
import { GlobalAdminProtectedRoute } from '~/shared/components/GlobalAdminProtectedRoute';
import { withAdminRoute } from '~/shared/hocs/withAdminRoute';
import { useDeliverableById } from '~/shared/hooks/useDeliverableById';

export default function DeliverableEditIndex() {
  const router = useRouter();
  const { id } = router.query;

  const { data: kpi, isLoading, isError } = useDeliverableById(id as string);

  if (isLoading || isError || !kpi) {
    return null;
  }

  return (
    <GlobalAdminProtectedRoute>
      <DeliverableForm.Page initialKpi={kpi} isEdit />
    </GlobalAdminProtectedRoute>
  );
}

export const getServerSideProps = withAdminRoute(async ctx => {
  const { id } = ctx.query;

  if (!id) {
    return {
      notFound: true,
    };
  }

  return {
    props: {},
  };
});
