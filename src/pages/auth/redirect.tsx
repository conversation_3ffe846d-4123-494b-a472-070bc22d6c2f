import { useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import { signIn } from 'next-auth/react';

export default function Redirect() {
  const hasRedirectedToSignIn = useRef(false);
  const router = useRouter();
  const basePath = process.env.NEXT_PUBLIC_BASE_PATH;
  const errorMessage = router.query.error;
  let callbackUrl = router.query.callbackUrl ?? `${basePath}/`;

  if (callbackUrl.includes('i18n')) {
    callbackUrl = `${basePath}/`;
  }

  useEffect(() => {
    if (errorMessage) {
      router.push(`${basePath}/auth/error`, { query: { error: errorMessage } });
    } else if (
      typeof callbackUrl === 'string' &&
      !hasRedirectedToSignIn.current
    ) {
      hasRedirectedToSignIn.current = true;

      signIn('azure-ad', { callbackUrl });
    }
  }, [callbackUrl, errorMessage, router]);

  return null;
}

Redirect.unauth = true;
