import {
  AbilityBuilder,
  AbilityClass,
  ConditionsMatcher,
  mongoQueryMatcher,
  PureAbility,
} from '@casl/ability';

enum Action {
  Create = 'create',
  Edit = 'edit',
  View = 'view',
  List = 'list',
  Manage = 'manage',
  Vote = 'vote',
  Facilitate = 'facilitate',
  Proxy = 'proxy',
  Unlock = 'unlock',
  Skip = 'skip',
  Delete = 'delete',
  Download = 'download',
}

export type Entities =
  | 'Home'
  | 'Proxy'
  | 'User'
  | 'Stellar'
  | 'Footer'
  | 'Cheershub'
  | 'Admin';

export type AbilityTuple = [`${Action}`, Entities | Record<string, unknown>];

export type AppAbility = PureAbility<AbilityTuple>;

export function createUserAbility(
  user: any,
  rules?: AppAbility['_indexedRules'],
) {
  const { can, build } = new AbilityBuilder(
    PureAbility as AbilityClass<AppAbility>,
  );

  const mainUserRoles = user?.roles;

  if (mainUserRoles?.isGlobalAdmin) {
    can(Action.Proxy, 'User');
    can(Action.Manage, 'Admin');
  }

  const ability = build({
    conditionsMatcher: mongoQueryMatcher as ConditionsMatcher<unknown>,
  });

  if (rules) {
    ability.update(rules);
  }

  return ability;
}
