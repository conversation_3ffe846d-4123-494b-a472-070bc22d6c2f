import { useSessionInfo } from '~/app/contexts/SessionInfoContext';

export function useUser() {
  const { sessionInfo, preferredLanguage } = useSessionInfo();

  const user = sessionInfo?.proxiedAs ?? sessionInfo;

  return {
    name: user?.name,
    zone: user?.zone,
    globalId: user?.globalId,
    employeeId: user?.employeeId,
    managedZones: user?.managedZones,
    roles: user?.roles,
    language: user?.language ?? preferredLanguage,
    persona: user?.persona,
    isProxying: user?.preferredLanguage,
    band: user?.band,
    isGlobalAdmin: user?.roles?.isGlobalAdmin ?? false,
  };
}

export type User = ReturnType<typeof useUser>;
