import { useQuery } from '@tanstack/react-query';

import deliverablesService from '../services/deliverables';
import { DeliverableItem } from '../types/Deliverable';

export const useDeliverableById = (uid: string | null | undefined) => {
  return useQuery<DeliverableItem>({
    queryKey: ['getDeliverableDetails', uid],
    queryFn: () => {
      if (!uid) {
        throw new Error('UID is required');
      }
      return deliverablesService.getDeliverableById(uid);
    },
    enabled: !!uid,
    retry: (failureCount, error: any) => {
      if (error?.response?.status === 404 || error?.response?.status === 403) {
        return false;
      }

      return failureCount < 3;
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};
