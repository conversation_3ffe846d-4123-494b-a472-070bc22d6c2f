import React, { PropsWithChildren } from 'react';
import InfiniteS<PERSON>roll from 'react-infinite-scroller';
import { Container, Skeleton } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

interface InfiniteScrollListProps extends PropsWithChildren {
  hasData: boolean;
  hasMoreData: boolean;
  loading: boolean;
  onLoadMore: () => void;
  loadingSkeletonCount?: number;
  loadingSkeletonHeight?: string;
  threshold?: number;
  useWindow?: boolean;
}

export function InfiniteScrollList({
  children,
  hasData,
  hasMoreData,
  loading,
  onLoadMore,
  loadingSkeletonCount = 3,
  loadingSkeletonHeight = '78px',
  threshold = 20,
  useWindow = false,
}: InfiniteScrollListProps) {
  const { t } = useTranslate();
  const hasMore = !loading && hasMoreData;

  if (!hasData && !loading) {
    return (
      <Container className="flex justify-center items-center py-8">
        <span>{t('message_no_results_found')}</span>
      </Container>
    );
  }

  return (
    <div style={{ overflow: 'auto', width: '100%' }}>
      <InfiniteScroll
        initialLoad={false}
        loadMore={onLoadMore}
        hasMore={hasMore}
        useWindow={useWindow}
        threshold={threshold}
        style={{ width: '100%' }}
      >
        {children}
        {loading && (
          <Container className="flex justify-center py-4">
            <Container className="grid gap-4 grid-cols-1">
              {Array.from({ length: loadingSkeletonCount }).map((_, index) => (
                <Skeleton
                  key={`infinite-loading-${index}`}
                  className="bg-gray-200"
                  style={{ height: loadingSkeletonHeight }}
                />
              ))}
            </Container>
          </Container>
        )}
      </InfiniteScroll>
    </div>
  );
}
