import {
  Activity,
  ArrowsAngleExpand,
  BarChart<PERSON>ill,
  Calculator,
  FileText,
  InfoCircle,
  Percent,
  PlusCircle,
  Trophy,
  XLg,
} from 'react-bootstrap-icons';
import { useRouter } from 'next/router';
import {
  Button,
  Container,
  Drawer,
  Separator,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { DELIVERABLE_MAPPED_TYPES } from '~/entities/DeliverableDetails/constants';
import { DeliverableTypeEnum } from '~/shared/types/Deliverable';
import { capitalizeFirstWord } from '~/shared/utils/capitalizeWord';

import { InfoRow } from '../InfoRow';
import { InfoRowDeliverables } from '../InfoRowDeliverables';
import { InfoRowGlobalOwner } from '../InfoRowGlobalOwner';

import type { CatalogItemDetailDrawerProps } from './types';

export function CatalogItemDetailDrawer({
  isOpen,
  onClose,
  itemId,
  itemData,
  itemDataDetails,
  kpiLoading,
}: CatalogItemDetailDrawerProps) {
  const { t } = useTranslate();
  const router = useRouter();

  const proxyUrl =
    window.location.hash === '#login-url'
      ? `?email=${router.query.email}&token=${router.query.token}#login-url`
      : '';

  const mappedType =
    DELIVERABLE_MAPPED_TYPES[
      itemData?.type as keyof typeof DeliverableTypeEnum
    ];

  const KPI_OR_PROJECT_YES_NO =
    itemDataDetails?.type === DeliverableTypeEnum.KPI ||
    itemDataDetails?.type === DeliverableTypeEnum.PROJECT_YES_NO;

  return (
    <Drawer.Root direction="right" open={isOpen} onOpenChange={onClose}>
      <Drawer.Content className="w-full max-w-lg pt-12">
        <Drawer.Title hidden />
        <Drawer.Description hidden />
        <Container className="overflow-y-auto h-full">
          <Container className="flex justify-between items-center p-5">
            <Container className="flex flex-col">
              <Typography variant="body-sm-bold">
                {t('common_details')}
              </Typography>
            </Container>
            <Button variant="tertiary" onClick={onClose}>
              <XLg size={20} />
            </Button>
          </Container>
          <Separator />
          <Container className="flex justify-between p-4">
            <InfoRow
              icon={InfoCircle}
              label={t('common_name')}
              value={itemData?.name}
              isLoading={false}
            />
            <InfoRow
              icon={BarChartFill}
              label={t('common_type')}
              value={capitalizeFirstWord(mappedType)}
              isLoading={false}
            />
          </Container>
          <Separator />
          <Container className="flex justify-between p-4">
            <InfoRow
              icon={Percent}
              label={t('common_function')}
              value={itemData?.businessFunction}
              isLoading={false}
            />
            <InfoRow
              icon={Activity}
              label={t('common_frequency')}
              value={itemDataDetails?.frequency}
              isLoading={kpiLoading}
            />
          </Container>
          <Separator />
          {KPI_OR_PROJECT_YES_NO && (
            <Container className="flex justify-between p-4">
              <InfoRow
                icon={Calculator}
                label={
                  itemDataDetails?.type === DeliverableTypeEnum.KPI
                    ? t('common_calculation_method')
                    : t('common_deliverable')
                }
                value={itemDataDetails?.calculationMethod}
                isLoading={false}
              />
            </Container>
          )}
          <Separator />
          <Container className="flex justify-between p-4">
            <InfoRow
              icon={Trophy}
              label={t('common_pa_value')}
              value={itemDataDetails?.paValue}
              isLoading={kpiLoading}
            />
          </Container>
          <Separator />
          <Container className="flex justify-between p-4">
            <InfoRow
              icon={FileText}
              label={t('common_definition')}
              value={itemDataDetails?.definition}
              isLoading={false}
            />
          </Container>
          <Separator />
          <Container className="flex justify-between p-4">
            <InfoRow
              icon={PlusCircle}
              label={t('common_bu_level_aggregation')}
              value={itemDataDetails?.buLevelAggregation}
              isLoading={kpiLoading}
            />
          </Container>
          <Separator />
          {(itemData?.type === DeliverableTypeEnum.PROJECT ||
            itemData?.type === DeliverableTypeEnum.MASTER_PROJECT) && (
            <>
              <Container className="flex justify-between p-4">
                <InfoRowDeliverables
                  value={itemDataDetails?.deliverables?.map(deliverable => ({
                    uid: String(deliverable.uid),
                    name: deliverable.name,
                  }))}
                  isLoading={kpiLoading}
                />
              </Container>
              <Separator />
            </>
          )}
          <Container className="flex justify-between p-4">
            <InfoRowGlobalOwner
              value={itemDataDetails?.owners?.map(owner => ({
                globalId: String(owner.globalId),
                name: owner.name,
              }))}
              isLoading={kpiLoading}
            />
          </Container>
          <Separator />
        </Container>
        <Drawer.Footer>
          <Container className="flex justify-between p-4">
            <Button
              variant="primary"
              className="w-full"
              iconRight={<ArrowsAngleExpand size={16} />}
              onClick={() => {
                if (itemId) {
                  router.push(`/deliverable-details/${itemId}${proxyUrl}`);
                }
              }}
            >
              <Typography variant="body-sm-bold">
                {t('common_view_kpi_page')}
              </Typography>
            </Button>
          </Container>
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer.Root>
  );
}
