import { useQuery } from '@tanstack/react-query';

import businessFunctionsService from '~/shared/services/businessFunctions';

export function useDeliverableFormCard() {
  const {
    data: businessFunctions = [],
    isLoading,
    isError,
  } = useQuery({
    queryKey: ['business-functions'],
    queryFn: () => businessFunctionsService.getBusinessFunctions(),
  });

  const functionOptions = (businessFunctions || []).map(bf => ({
    label: bf.label,
    value: bf.code,
  }));

  const frequencyOptions = [
    { label: 'Annualy', value: 'Annualy' },
    { label: 'Monthly', value: 'Monthly' },
    { label: 'Weekly', value: 'Weekly' },
    { label: 'Daily', value: 'Daily' },
  ];

  return {
    functionOptions,
    frequencyOptions,
    functionOptionsLoading: isLoading,
    functionOptionsError: isError,
  };
}
