import { useEffect } from 'react';
import { useRouter } from 'next/router';

import { usePermissions } from '~/shared/hooks/usePermissions';

export function GlobalAdminProtectedRoute({
  children,
  fallbackPath = '/',
}: GlobalAdminProtectedRouteProps) {
  const { isGlobalAdmin } = usePermissions();
  const router = useRouter();

  useEffect(() => {
    if (!isGlobalAdmin) {
      void router.replace(fallbackPath);
    }
  }, [isGlobalAdmin, router, fallbackPath]);

  if (!isGlobalAdmin) {
    return null;
  }

  return <>{children}</>;
}

export function useRouteProtection() {
  const { isGlobalAdmin } = usePermissions();
  const router = useRouter();

  const redirectIfNotAdmin = (fallbackPath: string = '/') => {
    if (!isGlobalAdmin) {
      void router.replace(fallbackPath);
      return false;
    }
    return true;
  };

  return {
    isGlobalAdmin,
    canAccessAdminRoutes: isGlobalAdmin,
    redirectIfNotAdmin,
  };
}
