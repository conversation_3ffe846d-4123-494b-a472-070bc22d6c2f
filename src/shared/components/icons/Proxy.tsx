type ProxyProps = {
  size?: number;
  color?: string;
};

export function Proxy({ size = 28, color = 'currentColor' }: ProxyProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 17 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 15C12.3473 15 12.6423 14.8783 12.885 14.635C13.1284 14.3922 13.25 14.0972 13.25 13.75C13.25 13.4028 13.1284 13.1078 12.885 12.865C12.6423 12.6217 12.3473 12.5 12 12.5C11.6528 12.5 11.3578 12.6217 11.115 12.865C10.8717 13.1078 10.75 13.4028 10.75 13.75C10.75 14.0972 10.8717 14.3922 11.115 14.635C11.3578 14.8783 11.6528 15 12 15ZM12 17.5C12.4167 17.5 12.8056 17.4028 13.1667 17.2083C13.5278 17.0139 13.8264 16.7431 14.0625 16.3958C13.7431 16.2014 13.4098 16.0592 13.0625 15.9692C12.7153 15.8786 12.3612 15.8333 12 15.8333C11.6389 15.8333 11.2848 15.8786 10.9375 15.9692C10.5903 16.0592 10.257 16.2014 9.93754 16.3958C10.1737 16.7431 10.4723 17.0139 10.8334 17.2083C11.1945 17.4028 11.5834 17.5 12 17.5ZM4.50004 6.66668H9.50004V5.00001C9.50004 4.30557 9.25698 3.71529 8.77087 3.22918C8.28476 2.74307 7.69448 2.50001 7.00004 2.50001C6.3056 2.50001 5.71532 2.74307 5.22921 3.22918C4.7431 3.71529 4.50004 4.30557 4.50004 5.00001V6.66668ZM7.20837 18.3333H2.00004C1.54171 18.3333 1.14948 18.1703 0.823374 17.8442C0.496707 17.5175 0.333374 17.125 0.333374 16.6667L0.333374 8.33334C0.333374 7.87501 0.496707 7.48251 0.823374 7.15584C1.14948 6.82973 1.54171 6.66668 2.00004 6.66668H2.83337L2.83337 5.00001C2.83337 3.84723 3.23976 2.86445 4.05254 2.05168C4.86476 1.23945 5.84726 0.833344 7.00004 0.833344C8.15282 0.833344 9.1356 1.23945 9.94837 2.05168C10.7606 2.86445 11.1667 3.84723 11.1667 5.00001V6.66668L12 6.66668C12.4584 6.66668 12.8509 6.82973 13.1775 7.15584C13.5037 7.48251 13.6667 7.87501 13.6667 8.33334V9.41668C13.4167 9.33334 13.1562 9.27084 12.885 9.22918C12.6145 9.18751 12.3195 9.16668 12 9.16668V8.33334L2.00004 8.33334L2.00004 16.6667H6.41671C6.52782 17 6.63893 17.2883 6.75004 17.5317C6.86115 17.7745 7.01393 18.0417 7.20837 18.3333ZM12 19.1667C10.8473 19.1667 9.86476 18.7606 9.05254 17.9483C8.23976 17.1356 7.83337 16.1528 7.83337 15C7.83337 13.8472 8.23976 12.8645 9.05254 12.0517C9.86476 11.2395 10.8473 10.8333 12 10.8333C13.1528 10.8333 14.1356 11.2395 14.9484 12.0517C15.7606 12.8645 16.1667 13.8472 16.1667 15C16.1667 16.1528 15.7606 17.1356 14.9484 17.9483C14.1356 18.7606 13.1528 19.1667 12 19.1667ZM2.00004 8.33334L2.00004 16.6667L2.00004 8.33334Z"
        fill={color}
      />
    </svg>
  );
}
