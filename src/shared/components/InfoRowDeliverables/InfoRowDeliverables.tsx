import { ListCheck } from 'react-bootstrap-icons';
import {
  Badge,
  Container,
  Skeleton,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { InfoRowDeliverablesProps } from './types';

export function InfoRowDeliverables({
  value,
  isLoading,
}: InfoRowDeliverablesProps) {
  const { t } = useTranslate();

  return (
    <Container className="flex align-top gap-4">
      <ListCheck size={20} />
      <Container className="flex flex-col gap-2">
        <Typography variant="body-sm-bold">
          {t('common_deliverables')}
        </Typography>
        <ul className="flex flex-col gap-2">
          {value &&
            value.map((item, index) => (
              <li key={index} className="flex items-center gap-2">
                {isLoading ? (
                  <Skeleton className="h-5 w-32 rounded" />
                ) : (
                  <Container className="flex items-center gap-2">
                    <Badge>
                      <Typography variant="metadata-sm-regular" color="light">
                        {item.name}
                      </Typography>
                    </Badge>
                  </Container>
                )}
              </li>
            ))}
        </ul>
      </Container>
    </Container>
  );
}
