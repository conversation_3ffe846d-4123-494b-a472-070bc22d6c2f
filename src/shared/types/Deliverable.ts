import { Owners } from './Owners';

export enum DeliverableTypeEnum {
  KPI = 'KPI',
  PROJECT = 'PROJECT',
  SCOPED_PROJECT_YES_NO = 'SCOPED_PROJECT_YES_NO',
  PROJECT_YES_NO = 'PROJECT_YES_NO',
  MASTER_PROJECT = 'MASTER_PROJECT',
}

export type DeliverableItem = {
  uid: string;
  name: string;
  businessFunction: string;
  frequency: string;
  isActive: boolean;
  calculationMethod: string;
  definition: string;
  paValue: string;
  dataSource: string;
  dateStart?: string | null;
  dateEnd?: string | null;
  type: DeliverableTypeEnum;
  buLevelAggregation: string;
  deliverables: DeliverableItem[];
  deliverableUids: [];
  scopedDeliverables?: CreateDeliverableItem[];
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string | null;
  usage?: number;
  owners?: Array<Owners>;
};

export type CreateDeliverableItem = Omit<
  DeliverableItem,
  'uid' | 'createdAt' | 'updatedAt' | 'createdBy' | 'updatedBy'
>;
