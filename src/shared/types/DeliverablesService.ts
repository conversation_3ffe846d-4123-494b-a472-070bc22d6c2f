import { CreateDeliverableItem, DeliverableItem } from './Deliverable';
import { GetDeliverableFilters } from './GetDeliverableFilters';

export type DeliverableItemsResponse = {
  data: DeliverableItem[];
  pageNumber: number;
  pageSize: number;
  totalRecords: number;
};

export interface IDeliverablesService {
  getDeliverables(
    filters: GetDeliverableFilters,
  ): Promise<DeliverableItemsResponse>;
  getDeliverableById(uid: string): Promise<DeliverableItem>;
  createDeliverable(data: CreateDeliverableItem): Promise<DeliverableItem>;
  updateDeliverable(
    uid: string,
    data: CreateDeliverableItem,
  ): Promise<DeliverableItem>;
  deleteDeliverable(uid: string): Promise<void>;
}
