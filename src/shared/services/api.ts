import { mountHTTPInstance } from '@ghq-abi/auth-client-lib';

export const api = () =>
  mountHTTPInstance({
    baseURL: process.env.NEXT_PUBLIC_NORTHSTAR_AUTH_API_URL,
  });

export const nextApi = () =>
  mountHTTPInstance({
    baseURL: process.env.NEXT_PUBLIC_BOILERPLATE_APP_URL,
  });

export const peoplePlatformApi = () =>
  mountHTTPInstance({
    baseURL: process.env.NEXT_PUBLIC_PEOPLE_PLATFORM_API_URL,
  });

export const northstarUserApi = () =>
  mountHTTPInstance({
    baseURL: process.env.NEXT_PUBLIC_NORTHSTAR_USER_API_URL,
  });

export const peoplePlatformPhotoApi = () =>
  mountHTTPInstance({
    baseURL: process.env.NEXT_PUBLIC_PEOPLE_PLATFORM_PHOTO_API_URL,
    responseType: 'blob',
  });
