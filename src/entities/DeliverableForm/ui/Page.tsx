import React from 'react';
import { ChevronLeft } from 'react-bootstrap-icons';
import { useRouter } from 'next/router';
import {
  Button,
  Container,
  FooterActionBar,
  Timeline,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';
import { FormikProvider } from 'formik';
import { KPI_FORM_ID } from '../constants';

import { ContentPage } from '~/app/templates/ContentPage';
import { ActionModal } from '~/shared/components/ActionModal';
import { useDeliverableForm } from '~/shared/hooks/useDeliverableForm';
import { FormikForm } from '../components/FormikForm';

export function Page({ initialKpi, isEdit }: any) {
  const { t } = useTranslate();
  const {
    formik,
    isLoading,
    handleBack,
    handleNext,
    step,
    setStep,
    defaultSteps,
    title,
    createDeliverableItemModal,
    createDeliverableItemConfirmationModal,
  } = useDeliverableForm({ initialKpi, isEdit });

  return (
    <>
      <ContentPage
        css={{
          '@lg': { pt: 0, px: '$md' },
        }}
        contentCss={{ bg: '$gray100', p: 0, gap: '$5' }}
        transparentContent
      >
        <Container className="flex flex-col gap-6 mb-20">
          <ActionModal
            isOpen={createDeliverableItemConfirmationModal.isOpen}
            openModal={createDeliverableItemConfirmationModal.openModal}
            closeModal={createDeliverableItemConfirmationModal.closeModal}
            title={createDeliverableItemConfirmationModal.title}
            message={createDeliverableItemConfirmationModal.message}
            actions={createDeliverableItemConfirmationModal.actions}
            variant={createDeliverableItemConfirmationModal.variant}
          />
          <ActionModal
            isOpen={createDeliverableItemModal.isOpen}
            openModal={createDeliverableItemModal.openModal}
            closeModal={createDeliverableItemModal.closeModal}
            title={createDeliverableItemModal.title}
            message={createDeliverableItemModal.message}
            actions={createDeliverableItemModal.actions}
            variant={createDeliverableItemModal.variant}
          />
          <Container className="flex gap-2 items-center">
            <Button
              className="p-0"
              variant="tertiary"
              onClick={() => window.history.back()}
              iconLeft={<ChevronLeft />}
            >
              <Typography variant="title-md-bold">{title}</Typography>
            </Button>
          </Container>
          <Container className="flex flex-col gap-2">
            <Timeline
              steps={defaultSteps}
              title={''}
              horizontalMode
              onStepClick={setStep}
            />
            <div className="border-b border-gray-200" />
          </Container>
          <FormikProvider value={formik}>
            <FormikForm onSubmit={formik.handleSubmit} formId={KPI_FORM_ID} setStep={setStep}>
              {defaultSteps[step].content}
            </FormikForm>
          </FormikProvider>
          <FooterActionBar>
            {step > 0 && (
              <Button
                variant="secondary"
                border="default"
                className="w-fit"
                onClick={handleBack}
              >
                {t('common_back')}
              </Button>
            )}
            {step !== defaultSteps.length - 1 && (
              <Button
                variant="secondary"
                border="default"
                type="button"
                onClick={handleNext}
                className="w-fit"
              >
                {t('common_next')}
              </Button>
            )}
            <Button
              id="deliverable-form"
              form="deliverable-form"
              type="submit"
              isLoading={isLoading}
              variant="primary"
              className="w-fit"
              round="md"
            >
              {t('common_submit')}
            </Button>
          </FooterActionBar>
        </Container>
      </ContentPage>
    </>
  );
}
