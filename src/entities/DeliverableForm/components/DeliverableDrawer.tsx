import { X } from 'react-bootstrap-icons';
import {
  <PERSON><PERSON>,
  Container,
  Drawer,
  Separator,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';
import { useFormikContext } from 'formik';

import { FormikInput } from '~/shared/components/FormikInput';
import {
  DeliverableItem,
  DeliverableTypeEnum,
} from '~/shared/types/Deliverable';

export interface DeliverableDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onAddDeliverable: (deliverable: DeliverableItem) => void;
}

export const DeliverableDrawer = ({
  isOpen,
  onClose,
  onAddDeliverable,
}: DeliverableDrawerProps) => {
  const { t } = useTranslate();
  const { values, handleChange, setFieldValue } = useFormikContext<any>();

  const handleAddDeliverable = async () => {
    if (!values.scopedDeliverables?.name?.trim()) {
      return;
    }

    const newDeliverable: DeliverableItem = {
      uid: `temp-${Date.now()}`,
      name: values.scopedDeliverables.name.trim(),
      isActive: true,
      businessFunction: ' ',
      calculationMethod: ' ',
      definition: ' ',
      frequency: ' ',
      paValue: ' ',
      dataSource: ' ',
      type: DeliverableTypeEnum.SCOPED_PROJECT_YES_NO,
      buLevelAggregation: '',
      deliverables: [],
      deliverableUids: [],
    };

    onAddDeliverable(newDeliverable);
    void setFieldValue('scopedDeliverables.name', '');
    onClose();
  };

  return (
    <Drawer.Root direction="right" open={isOpen} onOpenChange={onClose}>
      <Drawer.Content className="w-full max-w-lg pt-12">
        <Drawer.Title hidden />
        <Drawer.Description hidden />
        <Container className="overflow-y-auto h-full">
          <Container className="flex justify-between items-center p-4">
            <Container className="flex flex-col">
              <Typography variant="body-sm-bold">
                {t('common_create_project_initiative')}
              </Typography>
            </Container>
            <Button variant="tertiary" onClick={onClose}>
              <X size={20} />
            </Button>
          </Container>
          <Separator />
          <Container className="flex flex-col gap-4 p-5">
            <FormikInput
              name="scopedDeliverables.name"
              label="Name"
              placeholder={t('common_insert_here')}
              onChange={handleChange}
              value={values.scopedDeliverables?.name || ''}
            />
          </Container>
        </Container>
        <Separator />
        <Container className="flex justify-between p-5 mb-20">
          <Button variant="secondary" onClick={onClose} border="default">
            {t('common_cancel')}
          </Button>
          <Button
            variant="primary"
            onClick={handleAddDeliverable}
            disabled={!values.scopedDeliverables?.name?.trim()}
          >
            {t('common_add')}
          </Button>
        </Container>
      </Drawer.Content>
    </Drawer.Root>
  );
};
