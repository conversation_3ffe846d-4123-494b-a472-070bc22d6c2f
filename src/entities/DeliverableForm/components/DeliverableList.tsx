import React, { ReactNode, useState } from 'react';
import { Filter, Search } from 'react-bootstrap-icons';
import {
  Button,
  Container,
  Input,
  Skeleton,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { NoResults } from '~/shared/components';
import { DeliverableItem } from '~/shared/types/Deliverable';

interface TabConfig {
  id: string;
  label: string;
  count?: number;
}

export interface DeliverableListProps {
  deliverables: DeliverableItem[];
  title?: string;
  subtitle?: string;
  showSearch?: boolean;
  showFilters?: boolean;
  tabs?: TabConfig[];
  activeTab?: string;
  searchTerm?: string;
  isError?: boolean;
  isInitialLoading?: boolean;
  isSearchLoading?: boolean;
  onSearchChange?: (term: string) => void;
  onSearchKeyPress?: (e: React.KeyboardEvent) => void;
  onTabChange?: (tabId: string) => void;
  renderItem: (deliverable: DeliverableItem) => ReactNode;
  className?: string;
  containerClassName?: string;
  placeholder?: string;
}

export function DeliverableList({
  deliverables,
  title,
  subtitle,
  showSearch = false,
  showFilters = false,
  tabs,
  activeTab,
  searchTerm = '',
  onSearchChange,
  onSearchKeyPress,
  onTabChange,
  renderItem,
  className = '',
  containerClassName = 'w-96 bg-white rounded-lg p-6 flex flex-col',
  isError = false,
  isInitialLoading = false,
  isSearchLoading = false,
  placeholder,
}: DeliverableListProps) {
  const { t } = useTranslate();
  const [openFilters, setOpenFilters] = useState(false);

  return (
    <div className={`${containerClassName} ${className}`}>
      <div className="flex flex-col mb-4 flex-shrink-0">
        {(title || subtitle) && (
          <>
            {isInitialLoading && !isSearchLoading && title && (
              <Skeleton className="w-full h-6 mb-2" />
            )}
            {(!isInitialLoading || isSearchLoading) && title && (
              <Typography variant="body-sm-bold" className="text-gray-900 mb-2">
                {title}
              </Typography>
            )}

            {isInitialLoading && !isSearchLoading && subtitle && (
              <Skeleton className="w-full h-4 mb-2" />
            )}
            {(!isInitialLoading || isSearchLoading) && subtitle && (
              <Typography
                variant="body-sm-regular"
                className="text-gray-500 mb-4"
              >
                {subtitle}
              </Typography>
            )}
            <div className="border-t border-gray-200 mb-4" />
          </>
        )}

        {(showSearch || showFilters) && (
          <div className="flex gap-2 mb-4">
            {isInitialLoading && !isSearchLoading && showSearch && (
              <Skeleton className="w-full h-10" />
            )}
            {(!isInitialLoading || isSearchLoading) && showSearch && (
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder={placeholder || t('common_search_by_name_id')}
                  value={searchTerm}
                  onChange={e => onSearchChange?.(e.target.value)}
                  onKeyDown={onSearchKeyPress}
                  className="pl-10"
                  type="text"
                  autoComplete="off"
                />
              </div>
            )}
            {isInitialLoading && !isSearchLoading && showFilters && (
              <Skeleton className="w-12 h-10" />
            )}
            {(!isInitialLoading || isSearchLoading) && showFilters && (
              <Button
                variant="secondary"
                round="minimal"
                border="default"
                size="icon"
                onClick={() => setOpenFilters(!openFilters)}
                className="flex-shrink-0"
              >
                <Filter className="w-6 h-6" />
              </Button>
            )}
          </div>
        )}

        {isInitialLoading && openFilters && (
          <div className="flex flex-row gap-2">
            <Skeleton className="w-full h-10" />
            <Skeleton className="w-full h-10" />
            <Skeleton className="w-full h-10" />
          </div>
        )}
        {/* TODO: Add tabs DS and apply here */}
        {isInitialLoading && tabs && tabs.length > 0 && (
          <div className="flex flex-row gap-2">
            <Skeleton className="w-full h-10" />
            <Skeleton className="w-full h-10" />
          </div>
        )}
        {!isInitialLoading && tabs && tabs.length > 0 && (
          <div className="mb-4">
            <div className="flex border-b border-gray-200">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => onTabChange?.(tab.id)}
                  className={`
                    px-4 py-2 text-sm font-medium border-b-2 transition-colors
                    ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  {tab.label}
                  {tab.count !== undefined && (
                    <span
                      className={`ml-2 px-2 py-1 text-xs rounded-full ${
                        activeTab === tab.id
                          ? 'bg-blue-100 text-blue-600'
                          : 'bg-gray-100 text-gray-500'
                      }`}
                    >
                      {tab.count}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="space-y-3 flex-1 min-h-0 overflow-y-auto">
        {isError && (
          <Container className="flex justify-center items-center py-8">
            <Typography variant="metadata-sm-medium">
              {t('common_something_went_wrong')}
            </Typography>
          </Container>
        )}
        {isInitialLoading || isSearchLoading ? (
          Array.from({ length: 4 }).map((_, index) => (
            <Skeleton key={index} className="w-full h-[174px]" />
          ))
        ) : deliverables.length > 0 ? (
          deliverables.map(deliverable => (
            <div className="cursor-pointer mb-3" key={deliverable.uid}>
              {renderItem(deliverable)}
            </div>
          ))
        ) : (
          // TODO: Add empty state DS and apply here
          <NoResults />
        )}
      </div>
    </div>
  );
}
