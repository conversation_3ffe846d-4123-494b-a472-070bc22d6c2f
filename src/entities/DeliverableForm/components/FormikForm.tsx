import React, { useEffect } from 'react';
import { Form, useFormikContext } from 'formik';

interface FormikFormProps {
  onSubmit: (values: any) => void;
  formId: string;
  children: React.ReactNode;
  setStep: (step: number) => void;
}

export function FormikForm({onSubmit, formId, children, setStep}: FormikFormProps) {
  const { submitCount, errors } = useFormikContext()

  useEffect(() => {
    if (submitCount > 0 && Object.keys(errors).length) {
      setStep(0);
      // Adding timeout because submitCount changes before validation
      setTimeout(() => {
        const form = document.getElementById(formId) as HTMLFormElement;
        const formFields = form ? Array.from(form.elements) : [];
        const errorField = formFields.find(field => 
          field instanceof HTMLElement && field.className.includes('border-red-500')
        );
    
        if (errorField) {
          (errorField as HTMLElement).focus();
        }
      }, 100);
    }
  }, [submitCount, errors, setStep])

  return (
    <Form onSubmit={onSubmit} id={formId}>
      {children}
    </Form>
  );
}