import React from 'react';
import { Trash } from 'react-bootstrap-icons';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { Button, IconWrapper, Typography } from '@ghq-abi/design-system-v2';

import { Avatar } from '~/shared/components';
import { Employee } from '~/shared/types/Employee';

interface OwnersCardProps {
  employee: Employee;
  isDragging?: boolean;
  isSelected?: boolean;
  showDeleteButton?: boolean;
  disableDrag?: boolean;
  deleteEvent?: () => void;
}

export function OwnersCard({
  employee,
  isDragging = false,
  isSelected = false,
  showDeleteButton = false,
  disableDrag = false,
  deleteEvent,
}: OwnersCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging: dragging,
  } = useDraggable({
    id: employee.uuid,
    data: {
      employee,
    },
  });

  const style = {
    transform: CSS.Translate.toString(transform),
    opacity: dragging ? 0 : 1,
  };

  const fullName = employee.name
    ? employee.name
    : `${employee.firstName} ${employee.lastName}`;

  return (
    <div
      ref={!disableDrag ? setNodeRef : undefined}
      style={!disableDrag ? style : undefined}
      {...(!disableDrag ? listeners : {})}
      {...(!disableDrag ? attributes : {})}
      className={`
        relative bg-white border border-gray-200 rounded-lg p-4 max-h-[174px]
        ${!disableDrag ? 'cursor-grab' : ''}
        ${
          dragging || isDragging
            ? '!w-80  shadow-2xl z-[1000] cursor-grabbing scale-75'
            : 'w-full'
        }
        ${isSelected ? 'opacity-60 scale-98' : ''}
      `}
    >
      <div className="flex items-start flex-col gap-3">
        <div className="flex w-full justify-between">
          <div className="flex items-center gap-4">
            <div className="relative">
              <IconWrapper variant={'primary'} round="md" size={32}>
                <Avatar name={fullName} globalId={`${employee.globalId}`} />
              </IconWrapper>
            </div>
            <div className="flex flex-col">
              <Typography variant="body-sm-bold">{fullName}</Typography>
              <Typography variant="metadata-xs-regular">
                {employee.positionTitle}
              </Typography>
            </div>
          </div>
          {showDeleteButton && (
            <Button
              variant="tertiary"
              size="icon"
              onClick={() => deleteEvent?.()}
            >
              <Trash />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
