import { useEffect, useMemo, useState } from 'react';
import { Trash } from 'react-bootstrap-icons';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';
import { useTranslate } from '@tolgee/react';
import { useFormikContext } from 'formik';

import { ActionModal } from '~/shared/components/ActionModal';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import {
  DraggableArea,
  DraggableSortableCard,
} from '~/shared/components/DragAndDrop';
import { MappedSelectedData } from '~/shared/components/DragAndDrop/DraggableArea';
import { Employee } from '~/shared/types/Employee';
import { Owners } from '~/shared/types/Owners';

import { useOwner } from '../hooks/useOwner';
import { useOwnerFilter } from '../hooks/useOwnerFilter';

import { OwnersCard } from './OwnersCard';
import { OwnersList } from './OwnersList';

export function OwnersStep() {
  const { t } = useTranslate();
  const { values, setFieldValue } = useFormikContext<any>();
  const [selectedOwners, setSelectedOwners] = useState<Employee[]>([]);
  const actionModal = useActionModal();
  const [draggedOwner, setDraggedOwner] = useState<Employee | null>(null);
  const [isDraggingNewItem, setIsDraggingNewItem] = useState(false);
  const [mappedSelectedOwners, setMappedSelectedOwners] = useState<
    MappedSelectedData[]
  >([]);

  const {
    data: employees,
    isInitialLoading: isEmployeesInitialLoading,
    isSearchLoading: isEmployeesSearchLoading,
    isError: isEmployeesError,
    filters,
    setFilters,
  } = useOwner();

  const { searchQuery, handleSearchChange, handleSearchKeyPress } =
    useOwnerFilter(filters, setFilters);

  // When entering in this tab, check the if the form holds owners, if it holds,
  // convert it to Employee because Drag and Drop expects Employee data type.
  useEffect(() => {
    if (values.owners && values.owners.length > 0) {
      const filteredOwners = values.owners.map((owners: any): Employee => {
        const firstName = owners.name.split(' ')[0];
        const lastName = owners.name.split(' ').slice(1).join(' ');

        return {
          uuid: owners.uid,
          firstName,
          lastName,
          email: owners.email,
          positionTitle: owners.positionTitle,
          globalId: owners.globalId,
        };
      });

      setSelectedOwners(filteredOwners);
    }
  }, []);

  const availableEmployees = useMemo(() => {
    const selectedUids = new Set(selectedOwners.map(o => o.uuid));
    return (employees || []).filter(e => !selectedUids.has(e.uuid));
  }, [employees, selectedOwners]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3,
      },
    }),
  );

  const handleDragStart = (event: DragStartEvent) => {
    const employee = event.active.data.current?.employee;

    if (employee) {
      setDraggedOwner(employee);
      const isFromSideList = availableEmployees.some(
        e => e.uuid === employee.uuid,
      );
      setIsDraggingNewItem(isFromSideList);
    }
  };

  // Every time selectedOwners change, create a new array converting it from Employees[] to Owners[] and
  // set the array on owners from data because the Deliverables API expects a Owner and not a Employee.
  useEffect(() => {
    if (selectedOwners.length) {
      const filteredOwners = selectedOwners.map(
        (employee: Employee): Owners => {
          return {
            uid: employee.uuid,
            name: employee.firstName + ' ' + employee.lastName,
            positionTitle: employee.positionTitle,
            email: employee.email,
            globalId: employee.globalId,
          };
        },
      );
      setFieldValue('owners', filteredOwners);
    } else {
      setFieldValue('owners', []);
    }
  }, [selectedOwners.length]);

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) {
      setDraggedOwner(null);
      setIsDraggingNewItem(false);
      return;
    }

    const activeOwner = active.data.current?.employee;
    const isFromAvailableList =
      activeOwner && !selectedOwners.find(o => o.uuid === activeOwner.id);

    const isReordering =
      selectedOwners.find(o => o.uuid === over.id) &&
      selectedOwners.find(o => o.uuid === active.id);

    if (isReordering) {
      const oldIndex = selectedOwners.findIndex(o => o.uuid === active.id);
      const newIndex = selectedOwners.findIndex(o => o.uuid === over.id);

      if (oldIndex !== newIndex) {
        setSelectedOwners(prev => arrayMove(prev, oldIndex, newIndex));
      }
    }

    const isAdding =
      over.id === 'selection-area' ||
      (isFromAvailableList && selectedOwners.find(o => o.uuid === over.id));

    if (
      isAdding &&
      activeOwner &&
      !selectedOwners.find(o => o.uuid === activeOwner.id)
    ) {
      setSelectedOwners(prev => [...prev, activeOwner]);
    }

    setDraggedOwner(null);
    setIsDraggingNewItem(false);
  };

  const handleOwnerRemove = (ownerId: string) => {
    setSelectedOwners(prev => prev.filter(o => `${o.uuid}` !== ownerId));
  };

  const handleClearSelection = () => {
    setSelectedOwners([]);
  };

  const handleClearDeliverable = () => {
    actionModal.openModal({
      title: t('common_clear_list'),
      message: t('common_do_you_really_want_to_clear_the_list'),
      actions: [
        {
          label: t('common_yes'),
          onClick: () => {},
        },
        {
          label: t('common_no'),
          onClick: actionModal.closeModal,
          variant: 'secondary',
        },
      ],
      onConfirm: () => {
        handleClearSelection();
        actionModal.closeModal();
      },
    });
  };

  useEffect(() => {
    setMappedSelectedOwners(
      selectedOwners.map(owner => ({
        uid: owner.uuid,
      })),
    );
  }, [selectedOwners]);

  return (
    <DndContext
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      sensors={sensors}
    >
      <div className="flex gap-6 h-[calc(100vh-340px)]">
        <OwnersList
          employees={availableEmployees}
          title={t('common_employees_list')}
          subtitle={t('common_search_and_selected_employees')}
          showSearch
          searchTerm={searchQuery}
          onSearchChange={handleSearchChange}
          onSearchKeyPress={handleSearchKeyPress}
          isError={isEmployeesError}
          isInitialLoading={isEmployeesInitialLoading}
          isSearchLoading={isEmployeesSearchLoading}
          renderItem={employee => (
            <OwnersCard
              employee={employee}
              isSelected={selectedOwners.some(o => o.uuid === employee.uuid)}
            />
          )}
        />

        <DraggableArea
          type="OWNER"
          selectedData={mappedSelectedOwners}
          isDraggingNewItem={isDraggingNewItem}
          actions={[
            {
              label: t('common_clear_list'),
              onClick: handleClearDeliverable,
              variant: 'secondary',
              iconLeft: <Trash />,
              disabled: selectedOwners.length === 0,
            },
          ]}
        >
          {selectedOwners.map(employee => (
            <DraggableSortableCard key={employee.uuid} id={`${employee.uuid}`}>
              <OwnersCard
                disableDrag
                employee={employee}
                showDeleteButton
                deleteEvent={() => handleOwnerRemove(`${employee.uuid}`)}
              />
            </DraggableSortableCard>
          ))}
        </DraggableArea>

        <DragOverlay>
          {draggedOwner && <OwnersCard employee={draggedOwner} isDragging />}
        </DragOverlay>
        <ActionModal
          isOpen={actionModal.isOpen}
          openModal={actionModal.openModal}
          closeModal={actionModal.closeModal}
          title={actionModal.title}
          message={actionModal.message}
          actions={[
            {
              label: t('common_yes'),
              onClick: actionModal.handleConfirm,
              variant: 'primary',
            },
            {
              label: t('common_no'),
              onClick: actionModal.closeModal,
              variant: 'secondary',
            },
          ]}
        />
      </div>
    </DndContext>
  );
}
