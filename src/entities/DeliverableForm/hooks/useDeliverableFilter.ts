import { useState } from 'react';

export const useDeliverableFilter = (
  filters: { fuzzy_search?: string },
  setFilters: (filters: { fuzzy_search?: string }) => void,
) => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);

    if (!query.trim()) {
      setFilters({
        ...filters,
        fuzzy_search: '',
      });
    }
  };

  const handleSearchSubmit = () => {
    setFilters({
      ...filters,
      fuzzy_search: searchQuery,
    });
  };

  const handleSearchKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      e.stopPropagation();
      handleSearchSubmit();
    }
  };

  return {
    searchQuery,
    handleSearchChange,
    handleSearchSubmit,
    handleSearchKeyPress,
  };
};
