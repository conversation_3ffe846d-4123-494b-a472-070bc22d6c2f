import { useRef, useState } from 'react';
import { useDebounce } from 'react-use';
import { useQuery } from '@tanstack/react-query';

import employeeService from '~/shared/services/employee';

export const useOwner = () => {
  const [filters, setFilters] = useState<{ fuzzy_search: string }>({
    fuzzy_search: '',
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const isInitialLoad = useRef(true);

  const { data, isError, isFetching } = useQuery({
    queryKey: ['owners', filters],
    queryFn: () => employeeService.searchEmployees(filters.fuzzy_search),
    enabled: !!filters.fuzzy_search,
    onSuccess: () => {
      isInitialLoad.current = false;
    },
  });

  useDebounce(
    () => {
      setIsLoading(false);
    },
    500,
    [],
  );

  const isInitialLoading = isLoading && isInitialLoad.current;
  const isSearchLoading = isFetching && !isInitialLoad.current;

  return {
    data: data?.data || [],
    isLoading,
    isInitialLoading,
    isSearchLoading,
    isError,
    filters,
    setFilters,
  };
};
