import React from 'react';
import { Grid, ListUl } from 'react-bootstrap-icons';
import { ToggleGroup } from '@ghq-abi/design-system-v2';

import { CatalogListingTypeEnum } from '~/shared/utils/enums';

import { DisplayModeToggleProps } from './types';

export function DisplayModeToggle({
  displayMode,
  onToggleDisplayMode,
}: DisplayModeToggleProps) {
  return (
    <ToggleGroup.Root
      type="single"
      value={displayMode}
      className="flex items-center"
    >
      <ToggleGroup.Item
        value={CatalogListingTypeEnum.GRID}
        onClick={onToggleDisplayMode}
        className="p-3"
      >
        <Grid />
      </ToggleGroup.Item>
      <ToggleGroup.Item
        value={CatalogListingTypeEnum.LIST}
        onClick={onToggleDisplayMode}
        className="p-3"
      >
        <ListUl />
      </ToggleGroup.Item>
    </ToggleGroup.Root>
  );
}
