import React from 'react';
import { Search, X } from 'react-bootstrap-icons';
import { Button, Input } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

export function SearchBar({
  searchQuery,
  onSearchChange,
  onSearchKeyPress,
}: SearchBarProps) {
  const { t } = useTranslate();

  return (
    <div className="relative w-full">
      <div className="absolute top-1/2 left-3 -translate-y-1/2 pointer-events-none">
        <Search size={14} aria-hidden="true" />
      </div>
      <Input
        placeholder={t('common_search')}
        variant="filled"
        value={searchQuery}
        onChange={e => onSearchChange(e.target.value)}
        onKeyDown={onSearchKeyPress}
        className="flex-1 pl-8"
        aria-label="Search catalog items"
      />
      {searchQuery && (
        <Button
          className="absolute top-1/2 right-3 -translate-y-1/2 p-1 hover:bg-gray-200 rounded-full w-4 h-4 bg-transparent"
          onClick={() => onSearchChange('')}
          type="button"
        >
          <X size={14} aria-hidden="true" />
        </Button>
      )}
    </div>
  );
}
