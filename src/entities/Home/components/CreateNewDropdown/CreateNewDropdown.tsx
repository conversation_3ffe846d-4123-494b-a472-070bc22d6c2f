import React from 'react';
import { ChevronDown, Plus } from 'react-bootstrap-icons';
import { useRouter } from 'next/router';
import { Button, DropdownMenu, Typography } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { DeliverableTypeEnum } from '~/shared/types/Deliverable';

export function CreateNewDropdown() {
  const { t } = useTranslate();

  const router = useRouter();
  const navigateToCreateDeliverable = async (
    deliverableType: DeliverableTypeEnum,
  ) => {
    await router.push(`/deliverables/create?type=${deliverableType}`);
  };

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <Button
          variant="primary"
          size="sm"
          iconLeft={<Plus />}
          iconRight={<ChevronDown />}
        >
          {t('common_create_new')}
        </Button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content className="w-48">
        <DropdownMenu.Item
          onClick={() => navigateToCreateDeliverable(DeliverableTypeEnum.KPI)}
        >
          <Typography variant="body-sm-regular">
            {t('common_create_kpi')}
          </Typography>
        </DropdownMenu.Item>
        <DropdownMenu.Item
          onClick={() =>
            navigateToCreateDeliverable(DeliverableTypeEnum.PROJECT_YES_NO)
          }
        >
          <Typography variant="body-sm-regular">
            {t('common_create_single_project')}
          </Typography>
        </DropdownMenu.Item>
        <DropdownMenu.Item
          onClick={() =>
            navigateToCreateDeliverable(DeliverableTypeEnum.PROJECT)
          }
        >
          <Typography variant="body-sm-regular">
            {t('common_create_project')}
          </Typography>
        </DropdownMenu.Item>
        <DropdownMenu.Item
          onClick={() =>
            navigateToCreateDeliverable(DeliverableTypeEnum.MASTER_PROJECT)
          }
        >
          <Typography variant="body-sm-regular">
            {t('common_create_master_project')}
          </Typography>
        </DropdownMenu.Item>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
}
