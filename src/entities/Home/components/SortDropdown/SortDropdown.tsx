import React from 'react';
import { Filter } from 'react-bootstrap-icons';
import {
  <PERSON>ton,
  Container,
  DropdownMenu,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { SortType } from '../Filters/types';

type SortDropdownProps = {
  sortType: SortType | null | undefined;
  onSortChange: (value: SortType | null) => void;
};

export function SortDropdown({ sortType, onSortChange }: SortDropdownProps) {
  const { t } = useTranslate();

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <Button variant="tertiary" border="default" iconLeft={<Filter />}>
          <Typography variant="body-sm-regular">
            {t('common_sort_by')}
          </Typography>
        </Button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content className="w-48">
        <DropdownMenu.RadioGroup
          className="flex flex-col gap-2"
          value={sortType || ''}
          onValueChange={value => onSortChange(value as SortType)}
        >
          <DropdownMenu.RadioItem value={SortType.ASC} className="w-full">
            <Typography variant="body-sm-regular">{t('common_a_z')}</Typography>
          </DropdownMenu.RadioItem>
          <DropdownMenu.RadioItem value={SortType.DESC} className="w-full">
            <Typography variant="body-sm-regular">{t('common_z_a')}</Typography>
          </DropdownMenu.RadioItem>
        </DropdownMenu.RadioGroup>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
}
