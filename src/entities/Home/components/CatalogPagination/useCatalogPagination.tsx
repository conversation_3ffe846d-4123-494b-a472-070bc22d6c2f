export function useCatalogPagination(
  currentPage: number,
  totalPages: number,
  onPageChange: (page: number) => void,
) {
  const showPrevious = currentPage > 1;
  const showNext = currentPage < totalPages;
  const showEllipsis = totalPages > 7 && currentPage < totalPages - 3;

  const handlePageClick = (page: number) => (e: React.MouseEvent) => {
    e.preventDefault();
    onPageChange(page);
  };

  const getPagesToShow = () => {
    const maxPagesToShow = Math.min(totalPages, 7);
    const pages = [];

    for (let i = 0; i < maxPagesToShow; i++) {
      let pageNumberToShow = i + 1;

      if (totalPages > 7) {
        if (currentPage <= 4) {
          pageNumberToShow = i + 1;
        } else if (currentPage > totalPages - 4) {
          pageNumberToShow = totalPages - 6 + i;
        } else {
          pageNumberToShow = currentPage - 3 + i;
        }
      }

      if (pageNumberToShow >= 1 && pageNumberToShow <= totalPages) {
        pages.push(pageNumberToShow);
      }
    }

    return pages;
  };

  return {
    showPrevious,
    showNext,
    showEllipsis,
    handlePageClick,
    getPagesToShow,
  };
}
