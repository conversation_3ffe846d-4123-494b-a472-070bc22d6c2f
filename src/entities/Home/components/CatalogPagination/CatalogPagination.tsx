import React from 'react';
import { Container, Pagination } from '@ghq-abi/design-system-v2';

import { useCatalogPagination } from './useCatalogPagination';

export function CatalogPagination({
  currentPage,
  totalPages,
  onPageChange,
}: KpiPaginationProps) {
  const {
    showPrevious,
    showNext,
    showEllipsis,
    handlePageClick,
    getPagesToShow,
  } = useCatalogPagination(currentPage, totalPages, onPageChange);

  return (
    <Container className="flex justify-center mt-6">
      <Pagination.Root>
        <Pagination.Content>
          {showPrevious && (
            <Pagination.Item>
              <Pagination.Previous
                href="#"
                onClick={handlePageClick(currentPage - 1)}
              />
            </Pagination.Item>
          )}

          {getPagesToShow().map(pageNumber => (
            <Pagination.Item key={pageNumber}>
              <Pagination.Link
                href="#"
                isActive={pageNumber === currentPage}
                onClick={handlePageClick(pageNumber)}
              >
                {pageNumber}
              </Pagination.Link>
            </Pagination.Item>
          ))}

          {showEllipsis && (
            <Pagination.Item>
              <Pagination.Ellipsis />
            </Pagination.Item>
          )}

          {showNext && (
            <Pagination.Item>
              <Pagination.Next
                href="#"
                onClick={handlePageClick(currentPage + 1)}
              />
            </Pagination.Item>
          )}
        </Pagination.Content>
      </Pagination.Root>
    </Container>
  );
}
