import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ChevronDown,
  EyeSlash,
  List<PERSON>heck,
} from 'react-bootstrap-icons';
import {
  Button,
  Container,
  DropdownMenu,
  ToggleGroup,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { GlobalAdminProtectedFragment } from '~/shared/components';

import { useFilters, useKpiCatalog } from '../../hooks';

import { FiltersProps } from './types';

export const Filters = ({ filters, setFilters }: FiltersProps) => {
  const { t } = useTranslate();

  const {
    selectedFunctions,
    handleLevelChange,
    handleFunctionToggle,
    toggleValues,
  } = useFilters(filters, setFilters);

  const { businessFunctions } = useKpiCatalog();

  return (
    <Container className="flex justify-start gap-4 relative items-center">
      <ToggleGroup.Root
        type="multiple"
        className="flex items-center gap-4"
        value={toggleValues.filter((v): v is string => v !== null)}
        onValueChange={(value: string | string[]) => {
          const arrayValue = Array.isArray(value) ? value : [value];
          handleLevelChange(arrayValue);
        }}
      >
        <ToggleGroup.Item value="KPI" className="p-2 h-8 gap-2">
          <BarChartFill />
          <Typography variant="metadata-sm-bold">{t('common_kpi')}</Typography>
        </ToggleGroup.Item>
        <ToggleGroup.Item value="PROJECT" className="p-2 h-8 gap-2">
          <ListCheck />
          <Typography variant="metadata-sm-bold">
            {t('common_project')}
          </Typography>
        </ToggleGroup.Item>
        <div className="border-l-[1px] border-[#CACDD5] h-8" />
        <GlobalAdminProtectedFragment>
          <ToggleGroup.Item value="INACTIVE" className="p-2 h-8 gap-2">
            <EyeSlash />
            <Typography variant="metadata-sm-bold">
              {t('common_status_inactive')}
            </Typography>
          </ToggleGroup.Item>
        </GlobalAdminProtectedFragment>
      </ToggleGroup.Root>
      <DropdownMenu.Root modal={false}>
        <DropdownMenu.Trigger asChild>
          <Button
            variant={selectedFunctions.length > 0 ? 'light' : 'secondary'}
            border="default"
            iconRight={<ChevronDown />}
            className="p-2 h-8 gap-2"
          >
            <Typography variant="metadata-sm-bold">
              {t('common_functions')}
            </Typography>
          </Button>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content className="w-56">
          {businessFunctions.map(functionName => (
            <DropdownMenu.CheckboxItem
              key={functionName}
              onSelect={e => {
                e.preventDefault();
                handleFunctionToggle(functionName);
              }}
              checked={selectedFunctions.includes(functionName)}
            >
              {functionName}
            </DropdownMenu.CheckboxItem>
          ))}
        </DropdownMenu.Content>
      </DropdownMenu.Root>
    </Container>
  );
};
