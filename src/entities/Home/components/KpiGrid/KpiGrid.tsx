import React from 'react';
import { Container, Skeleton } from '@ghq-abi/design-system-v2';

import { CatalogItemCard } from '~/shared/components/CatalogCard';
import { cn } from '~/shared/utils/cn';
import { CatalogListingTypeEnum } from '~/shared/utils/enums';

import { KpiGridProps } from './types';

export function KpiGrid({
  kpis,
  isLoading,
  displayMode,
  onItemDetails,
  onOpenDrawer,
}: KpiGridProps) {
  const gridClasses =
    displayMode === CatalogListingTypeEnum.GRID
      ? 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
      : 'grid-cols-1';

  if (isLoading || !kpis) {
    return (
      <Container className={`grid gap-4 ${gridClasses}`}>
        {Array.from({ length: 18 }).map((_, index) => (
          <Skeleton
            key={index}
            className={cn('h-[70px] bg-gray-200', {
              'h-[78px]': displayMode === CatalogListingTypeEnum.LIST,
            })}
          />
        ))}
      </Container>
    );
  }

  return (
    <Container className={`grid gap-4 ${gridClasses}`}>
      {kpis &&
        kpis.data.map((item: any) => (
          <CatalogItemCard
            key={item.uid}
            item={item}
            displayMode={displayMode}
            onCatalogItemDetails={onItemDetails}
            onOpenCatalogItemDrawer={onOpenDrawer}
          />
        ))}
    </Container>
  );
}
