import React from 'react';
import { Search as SearchIcon } from 'react-bootstrap-icons';
import Head from 'next/head';
import { <PERSON><PERSON>, Card, Container, Typography } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { ContentPage } from '~/app/templates/ContentPage';
import { CatalogItemDetailDrawer } from '~/shared/components/CatalogItemDetailDrawer';
import { GlobalAdminProtectedFragment } from '~/shared/components/GlobalAdminProtectedFragment';
import { InfiniteScrollList } from '~/shared/components/InfiniteScrollList';
import { DeliverableItemsResponse } from '~/shared/types/DeliverablesService';
import { CatalogListingTypeEnum } from '~/shared/utils/enums';

import {
  CatalogPagination,
  CreateNewDropdown,
  DisplayModeToggle,
  KpiGrid,
  SearchBar,
  SortDropdown,
} from '../components';
import { Filters } from '../components/Filters';
import { useFilters, useKpiCatalog, usePagination } from '../hooks';

interface PageProps {
  initialKpis: DeliverableItemsResponse;
}

export function Page({ initialKpis }: PageProps) {
  const { t } = useTranslate();

  const { totalPages, currentPage, setPageNumber, pageSize } = usePagination({
    initialPageSize: 12,
    totalRecords: 0,
  });

  const {
    displayMode,
    toggleDisplayMode,
    kpis,
    kpisLoading,
    kpiData,
    kpiLoading,
    handleNavigateToItemDetails,
    isDrawerOpen,
    selectedItemId,
    handleOpenDrawer,
    handleCloseDrawer,
    filters,
    setFilters,
    kpisError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useKpiCatalog(currentPage, pageSize, initialKpis);

  React.useEffect(() => {
    if (kpis?.totalRecords) {
    }
  }, [kpis?.totalRecords]);

  // Fetch specific page for GRID mode when page changes
  React.useEffect(() => {
    if (displayMode === CatalogListingTypeEnum.GRID && currentPage > 1) {
      fetchNextPage();
    }
  }, [currentPage, displayMode, fetchNextPage]);

  const actualTotalPages = kpis?.totalRecords
    ? Math.ceil(kpis.totalRecords / pageSize)
    : totalPages;

  const {
    searchQuery,
    handleSearchChange,
    handleSearchKeyPress,
    handleSearchSubmit,
    sortType,
    handleSortChange,
  } = useFilters(filters, setFilters, setPageNumber);

  const isError = !!kpisLoading && kpisError;

  return (
    <>
      <Head>
        <title>{t('home.title')}</title>
      </Head>

      <ContentPage
        css={{
          '@lg': { pt: 0, px: '$md' },
        }}
        contentCss={{ bg: '$gray100', p: 0, gap: '$5' }}
        transparentContent
      >
        <CatalogItemDetailDrawer
          isOpen={isDrawerOpen}
          onClose={handleCloseDrawer}
          itemId={selectedItemId}
          itemData={kpis && kpis.data.find(item => item.uid === selectedItemId)}
          itemDataDetails={kpiData}
          kpiLoading={kpiLoading}
        />

        <Container className="flex flex-col gap-4">
          <Container className="flex justify-between">
            <Typography variant="title-md-bold">
              {t('common_catalog')}
            </Typography>
            <Container className="flex gap-2 justify-end">
              <DisplayModeToggle
                displayMode={displayMode}
                onToggleDisplayMode={toggleDisplayMode}
              />
            </Container>
          </Container>

          <Card.Root className="flex items-center p-4 rounded-xl gap-4">
            <SearchBar
              searchQuery={searchQuery}
              onSearchChange={handleSearchChange}
              onSearchKeyPress={handleSearchKeyPress}
            />
            <Button
              variant="tertiary"
              border="default"
              size="icon"
              aria-label={t('common_search')}
              onClick={handleSearchSubmit}
            >
              <SearchIcon />
            </Button>
            <SortDropdown sortType={sortType} onSortChange={handleSortChange} />
          </Card.Root>

          <Container className="flex items-center max-h-8">
            <Filters filters={filters} setFilters={setFilters} />
            <GlobalAdminProtectedFragment>
              <CreateNewDropdown />
            </GlobalAdminProtectedFragment>
          </Container>

          {isError && (
            <Container className="flex justify-center items-center py-8">
              <Typography variant="body-md-medium">
                {t('common_something_went_wrong')}
              </Typography>
            </Container>
          )}

          {displayMode === CatalogListingTypeEnum.LIST ? (
            <InfiniteScrollList
              hasData={!!kpis?.data?.length}
              hasMoreData={!!hasNextPage}
              loading={!!isFetchingNextPage}
              onLoadMore={() => fetchNextPage?.()}
              threshold={20}
              useWindow={true}
            >
              <KpiGrid
                kpis={kpis}
                isLoading={kpisLoading}
                displayMode={displayMode}
                onItemDetails={handleNavigateToItemDetails}
                onOpenDrawer={handleOpenDrawer}
              />
            </InfiniteScrollList>
          ) : (
            <KpiGrid
              kpis={kpis}
              isLoading={kpisLoading}
              displayMode={displayMode}
              onItemDetails={handleNavigateToItemDetails}
              onOpenDrawer={handleOpenDrawer}
            />
          )}

          {displayMode === CatalogListingTypeEnum.GRID &&
            !kpisLoading &&
            kpis &&
            actualTotalPages > 1 && (
              <Container className="flex justify-center py-4 mt-8">
                <CatalogPagination
                  currentPage={currentPage}
                  totalPages={actualTotalPages}
                  onPageChange={setPageNumber}
                />
              </Container>
            )}
        </Container>
      </ContentPage>
    </>
  );
}
