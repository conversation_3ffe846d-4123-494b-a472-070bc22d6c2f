import { SignInResponse } from 'next-auth/react';
import { useMutation } from '@tanstack/react-query';

type CreateProxyPayload = {
  proxyEmployeeGlobalId: string;
};

type UseCreateProxyOptions = {
  mutationFn?: (data: CreateProxyPayload) => Promise<SignInResponse>;
  onSuccess?: (data: SignInResponse) => void;
  onError?: () => void;
};

export function useCreateProxy({
  mutationFn,
  onSuccess,
  onError,
}: UseCreateProxyOptions = {}) {
  return useMutation({ mutationFn, onSuccess, onError });
}
