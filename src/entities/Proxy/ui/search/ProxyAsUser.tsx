import * as React from 'react';
import { Flex } from '@ghq-abi/design-system';

import { Employee } from '../../types';

import { EmployeeSearchBox } from './EmployeeSearchBox';
import { SelectedEmployeeBox } from './SelectedEmployeeBox';

export function ProxyAsUser() {
  const [selectedEmployee, setSelectedEmployee] =
    React.useState<Employee | null>(null);

  return (
    <Flex
      css={{ height: '$full', '@xl': { flexDirection: 'row' } }}
      gap="md"
      direction="column"
    >
      <Flex
        css={{
          width: '$full',
          '@xl': { width: '$sm' },
          '@2xl': { width: '$md' },
        }}
      >
        <EmployeeSearchBox
          onSelectEmployee={employee => setSelectedEmployee(employee)}
          selectedEmployee={selectedEmployee}
        />
      </Flex>
      <Flex css={{ '@xl': { flexGrow: '1' } }}>
        <SelectedEmployeeBox
          employee={selectedEmployee}
          onResetEmployee={() => setSelectedEmployee(null)}
        />
      </Flex>
    </Flex>
  );
}
