import * as React from 'react';
import { IoIosClose, IoMdSearch } from 'react-icons/io';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useDebounce } from 'react-use';
import {
  Flex,
  IconButton,
  reusables,
  Skeleton,
  TextField,
  theme,
} from '@ghq-abi/design-system';
import { useTranslate } from '@tolgee/react';

import { PersonalProfile } from '~/shared/components';

import { GetEmployeesFilterParams, useInfinityGetEmployees } from '../../api';
import { Employee } from '../../types';

import { EmployeeCardContainer } from './styles';

const Skeletons = ({ size = 5 }) => (
  <>
    {Array.from({ length: size }, (_, i) => i).map(i => (
      <Skeleton
        key={i}
        variant="rectangle"
        css={{ width: '$full', minHeight: 56, m: 0 }}
      />
    ))}
  </>
);

const infiniteScrollRefId = 'proxy-employee-list';

type EmployeeSearchBoxProps = {
  onSelectEmployee: (employee: Employee) => void;
  selectedEmployee: Employee | null;
};

export function EmployeeSearchBox({
  selectedEmployee,
  onSelectEmployee,
}: EmployeeSearchBoxProps) {
  const { t } = useTranslate(['default', 'proxy']);

  const [query, setQuery] = React.useState('');
  const [filters, setFilters] = React.useState<GetEmployeesFilterParams>({});
  const employeesQuery = useInfinityGetEmployees({
    employeeNameOrId: filters.employeeNameOrId || '',
  });

  useDebounce(
    () => {
      setFilters(state => ({
        ...state,
        employeeNameOrId: query.trim(),
      }));
    },
    500,
    [query],
  );

  const employees = [
    ...(employeesQuery.data?.pages.map(page => page.data).flat() ?? []),
  ];

  function clearQueryFilter() {
    setFilters(state => ({ ...state, employeeNameOrId: '' }));
    setQuery('');
  }

  return (
    <Flex
      direction="column"
      css={{
        height: '$80',
        width: '$full',
        '@xl': { height: '70vh' },
      }}
    >
      <TextField
        containerCss={{ margin: '$sm' }}
        placeholder={t('proxy.search.filter_placeholder', { ns: 'proxy' })}
        rightIcon={
          <Flex
            css={{
              position: 'relative',
              width: theme.sizes[6].value,
            }}
            align="center"
          >
            <IoMdSearch
              style={{
                position: 'absolute',
                width: theme.sizes[6].value,
                height: theme.sizes[6].value,
                transform: !filters.employeeNameOrId
                  ? 'translateX(0)'
                  : 'translateX(10px)',
                opacity: !filters.employeeNameOrId ? '1' : '0',
                transition: theme.transitions['slowly-ease'].value,
              }}
            />
            <IconButton
              onClick={clearQueryFilter}
              title={t('common.clear_field', { ns: 'default' })}
              css={{
                border: 'none',
                transform: filters.employeeNameOrId
                  ? 'translateX(0)'
                  : 'translateX(10px)',
                opacity: filters.employeeNameOrId
                  ? '1 !important'
                  : '0 !important',
                pointerEvents: filters.employeeNameOrId ? 'all' : 'none',
                transition: theme.transitions['slowly-ease'].value,
              }}
              icon={
                <IoIosClose
                  style={{
                    position: 'absolute',
                    width: theme.sizes[8].value,
                    height: theme.sizes[8].value,
                  }}
                />
              }
            />
          </Flex>
        }
        onChange={e => setQuery(e.target.value)}
        value={query}
      />
      <Flex
        css={{
          height: '$80',
          width: '$full',
          overflowY: 'auto',
          '@xl': { height: '70vh' },
        }}
        className={reusables.scrollbar()}
        gap="md"
        direction="column"
        id={infiniteScrollRefId}
      >
        <InfiniteScroll
          scrollableTarget={infiniteScrollRefId}
          next={employeesQuery.fetchNextPage}
          hasMore={!!employeesQuery.hasNextPage}
          loader={<Skeletons size={1} />}
          dataLength={employees.length}
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: theme.space.sm.value,
            padding: theme.space.sm.value,
          }}
        >
          {employees.map(employee => (
            <EmployeeCardContainer
              key={employee.id}
              active={selectedEmployee?.id === employee.id}
              onClick={() => onSelectEmployee(employee)}
            >
              <PersonalProfile
                firstName={employee?.firstName}
                globalId={employee.employeeGlobalId}
                jobTitle={[employee.employeeGlobalId, employee.zone?.name || '']
                  .filter(Boolean)
                  .join(' - ')}
                lastName={employee?.lastName}
                name={employee.firstName + ' ' + employee.lastName}
              />
            </EmployeeCardContainer>
          ))}

          {employeesQuery.isLoading && <Skeletons size={20} />}
        </InfiniteScroll>
      </Flex>
    </Flex>
  );
}
