import { useRouter } from 'next/router';
import { signIn, SignInResponse } from 'next-auth/react';
import {
  AuthenticatedUser,
  CredentialProvidersEnum,
} from '@ghq-abi/auth-client-lib';
import { Button, Flex, Heading, Text } from '@ghq-abi/design-system';
import { useTranslate } from '@tolgee/react';
import nprogress from 'nprogress';

import { useFlags } from '~/app/contexts/FlagsContext';
import { useSessionInfo } from '~/app/contexts/SessionInfoContext';
import { Avatar, ErrorText, Icon } from '~/shared/components';

import { useCreateProxy } from '../../api';
import { Employee } from '../../types';

type SelectedEmployeeBoxProps = {
  employee?: Employee | null;
  onResetEmployee: () => void;
};

export function SelectedEmployeeBox({
  employee,
  onResetEmployee,
}: SelectedEmployeeBoxProps) {
  const { t } = useTranslate(['proxy', 'default']);
  const flags = useFlags();
  const router = useRouter();
  const { sessionInfo } = useSessionInfo();

  const createProxyMutation = useCreateProxy();

  const createProxy = useCreateProxy({
    async mutationFn({ proxyEmployeeGlobalId }) {
      nprogress.start();

      const user = JSON.stringify({
        employeeId: sessionInfo.employeeId,
        proxiedAs: { globalId: proxyEmployeeGlobalId },
      } as AuthenticatedUser);

      try {
        const signInResponse = await signIn(
          CredentialProvidersEnum.REGISTER_PROXY,
          {
            user,
            callbackUrl: '/',
          },
        );

        return signInResponse as SignInResponse;
      } catch (_) {
        throw new Error(`Something went wrong. Couldn't proxy as employee.`);
      }
    },
    async onSuccess() {
      nprogress.done();
    },
    async onError() {
      nprogress.done();
    },
  });

  const handleStartProxy = async () => {
    if (!sessionInfo || !employee) {
      return;
    }

    createProxy.mutate({
      proxyEmployeeGlobalId: employee.employeeGlobalId,
    });
  };

  return (
    <Flex
      css={{ height: '$full', flexGrow: 1 }}
      direction="column"
      align="center"
      gap="md"
    >
      <Flex
        direction="column"
        align="center"
        css={{ width: '$full', mt: '$xl' }}
        gap="sm"
      >
        <Heading
          size="lg"
          css={{ textAlign: 'center', fontSize: '$2xl', color: '$gray780' }}
        >
          {t('proxy.selected.helper_title')}
        </Heading>
        <Text css={{ fontSize: '$xl', color: '$gray780' }}>
          {t('proxy.selected.helper_text')}
        </Text>
      </Flex>

      {employee ? (
        <>
          <Flex
            css={{ width: '$full', mt: '$xl', p: '$md' }}
            align="center"
            justify="center"
            gap={{ '@initial': 'md', '@xl': 'xl' }}
          >
            <Avatar
              globalId={employee.employeeGlobalId}
              name={employee.firstName + ' ' + employee.lastName}
            />
            <Text
              as="span"
              css={{
                fontSize: '$lg',
                textTransform: 'uppercase',
              }}
            >
              <Text
                as="span"
                css={{
                  fontWeight: '$bold',
                  fontSize: 'inherit',
                }}
              >
                {employee?.firstName}{' '}
              </Text>
              {employee?.lastName}
            </Text>
          </Flex>

          {createProxyMutation.error ? (
            <ErrorText>
              {(createProxyMutation.error as Error).message}
            </ErrorText>
          ) : null}

          <Flex
            css={{ width: '$full', mt: '$xl' }}
            align="center"
            justify="center"
            gap="lg"
          >
            <Button
              css={{ fontSize: '$md' }}
              leftIcon={<Icon.StartProxy size={20} />}
              onClick={handleStartProxy}
              loading={createProxyMutation.isLoading}
              disabled={createProxyMutation.isLoading}
            >
              {t('proxy.selected.init_proxy_button')}
            </Button>
            <Button
              variant="secondary"
              css={{ fontSize: '$md' }}
              onClick={onResetEmployee}
              disabled={createProxyMutation.isLoading}
            >
              {t('common.cancel', { ns: 'default' })}
            </Button>
          </Flex>
        </>
      ) : null}
    </Flex>
  );
}
