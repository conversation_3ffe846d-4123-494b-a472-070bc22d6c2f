import { Box, styled } from '@ghq-abi/design-system';

export const EmployeeCardContainer = styled(Box, {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  height: '$14',
  borderRadius: '$default',
  gap: '$sm',
  p: '$sm',
  cursor: 'pointer',
  border: 0,
  bg: '$gray300',
  textAlign: 'left',
  transition: '$quickly-ease',
  '&:hover': { filter: 'brightness(0.92)' },
  '&:active': { filter: 'brightness(0.92)' },

  variants: {
    active: {
      true: {
        filter: 'brightness(0.92)',
      },
    },
  },
});
