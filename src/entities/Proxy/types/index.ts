export type EmployeeZone = {
  id: string;
  name: string;
};

export type Employee = {
  email: string;
  employee_band: string;
  employee_band_group: string;
  employeeGlobalId: string;
  firstName: string;
  function: string;
  id: string;
  lastName: string;
  manager?: Omit<Employee, 'manager'>;
  partner_status: boolean;
  position: string;
  slt_level: string;
  workCountry: string;
  status: string;
  jobTitle?: string;
  area?: string;
  zone: EmployeeZone | null;
  timeInCompanyYear: string | null;
  timeInCompanyMonth: string | null;
  eligible: boolean;
};

export type EmployeeSimple = {
  id: string;
  name: string;
  globalId: string;
  eligible: boolean;
  band: string;
  workCountry: string;
  position: string;
  managerName: string;
  zoneName: string;
  hireDate: string | null;
};
