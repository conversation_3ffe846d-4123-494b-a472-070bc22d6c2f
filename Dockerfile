# check=skip=SecretsUsedInArgOrEnv
# 1. Build application
FROM node:20-alpine AS builder

# Modules
ENV NEXT_PUBLIC_MY_COMPENSATION_URL=VAR_NEXT_PUBLIC_MY_COMPENSATION_URL
ENV NEXT_PUBLIC_MY_TALENT_CARD_URL=VAR_NEXT_PUBLIC_MY_TALENT_CARD_URL
ENV NEXT_PUBLIC_LCM_URL=VAR_NEXT_PUBLIC_LCM_URL
ENV NEXT_PUBLIC_CHEERSHUB_URL=VAR_NEXT_PUBLIC_CHEERSHUB_URL
ENV NEXT_PUBLIC_WORKDAY_URL=VAR_NEXT_PUBLIC_WORKDAY_URL

# MICROFRONT
ENV NEXTAUTH_URL=VAR_NEXTAUTH_URL
ENV PP_AUTH_LIB_AZURE_REDIRECT_URI=VAR_PP_AUTH_LIB_AZURE_REDIRECT_URI
ENV NEXT_PUBLIC_APP_URL=VAR_NEXT_PUBLIC_APP_URL

# This needs to be setted to for build the app
ENV NEXT_PUBLIC_BASE_PATH="/catalog/new-kpi-catalog"

# PP AUTH LIB
ENV PP_AUTH_LIB_REDIS_URL=VAR_PP_AUTH_LIB_REDIS_URL
ENV PP_AUTH_LIB_AZURE_CLIENT_ID=VAR_PP_AUTH_LIB_AZURE_CLIENT_ID
ENV PP_AUTH_LIB_AZURE_CLIENT_SECRET=VAR_PP_AUTH_LIB_AZURE_CLIENT_SECRET
ENV NEXT_PUBLIC_PP_AUTH_LIB_AZURE_TENANT_ID=VAR_NEXT_PUBLIC_PP_AUTH_LIB_AZURE_TENANT_ID
ENV PP_AUTH_LIB_AZURE_DEBUG=VAR_PP_AUTH_LIB_AZURE_DEBUG
ENV PP_AUTH_LIB_AZURE_HTTP_TIMEOUT=VAR_PP_AUTH_LIB_AZURE_HTTP_TIMEOUT
ENV PP_AUTH_LIB_SYSTEM_ENUM=VAR_PP_AUTH_LIB_SYSTEM_ENUM
ENV NEXTAUTH_SECRET=VAR_NEXTAUTH_SECRET

# Northstar Auth API
ENV NEXT_PUBLIC_NORTHSTAR_AUTH_API_URL=VAR_NEXT_PUBLIC_NORTHSTAR_AUTH_API_URL

# Kpi Catalog API V2
ENV NEXT_PUBLIC_API_URL=VAR_NEXT_PUBLIC_API_URL

# Photo,Auth,Platform API
ENV NEXT_PUBLIC_AUTH_API_URL=VAR_NEXT_PUBLIC_AUTH_API_URL
ENV NEXT_PUBLIC_AUTH_API_URL_INTERNAL=VAR_NEXT_PUBLIC_AUTH_API_URL_INTERNAL
ENV NEXT_PUBLIC_PEOPLE_PLATFORM_API_URL=VAR_NEXT_PUBLIC_PEOPLE_PLATFORM_API_URL
ENV NEXT_PUBLIC_PEOPLE_PLATFORM_PHOTO_API_URL=VAR_NEXT_PUBLIC_PEOPLE_PLATFORM_PHOTO_API_URL

# Tolgee
ENV NEXT_PUBLIC_TOLGEE_CONTENT_DELIVERY_URL=VAR_NEXT_PUBLIC_TOLGEE_CONTENT_DELIVERY_URL
ENV NEXT_PUBLIC_TOLGEE_API_URL=VAR_NEXT_PUBLIC_TOLGEE_API_URL
ENV NEXT_PUBLIC_TOLGEE_API_KEY=VAR_NEXT_PUBLIC_TOLGEE_API_KEY
ENV NEXT_PUBLIC_NORTHSTAR_USER_API_URL=VAR_NEXT_PUBLIC_NORTHSTAR_USER_API_URL

# Google Analytics
ENV NEXT_PUBLIC_GA_ID=VAR_NEXT_PUBLIC_GA_ID

# Set the working directory
WORKDIR /usr/src/app

# Bundle app source
COPY  . .

# Install all deps
RUN npm i

# Builds the app
RUN npm run build

# 2. Create a runner image
FROM node:20-alpine AS runner

# Set the working directory
WORKDIR /usr/src/app

# Set the user to node
USER node

# Set NODE_ENV environment variable
ENV NODE_ENV=production

# Copy only the necessary files from the builder stage to run the app
COPY --from=builder --chown=node:node /usr/src/app/public ./public
COPY --from=builder --chown=node:node /usr/src/app/.next/standalone ./
COPY --from=builder --chown=node:node /usr/src/app/.next/static ./.next/static

COPY node-start.sh .

# Expose the port the app runs on
EXPOSE 3000

# Start the application
ENTRYPOINT ["sh", "node-start.sh"]
