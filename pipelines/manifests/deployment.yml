apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: '$(k8sNamespace)'
  name: '$(appName)'
  labels:
    k8s-app: '$(appName)'
    tags.datadoghq.com/env: '$(environmentAcronym)' # Unified service tag - Deployment Env tag
    tags.datadoghq.com/service: '$(appName)' # Unified service tag - Deployment Service tag
    tags.datadoghq.com/version: '$(tag)' # Unified service tag - Deployment Version tag
spec:
  selector:
    matchLabels:
      k8s-app: '$(appName)'
  replicas: $(replicas)
  template:
    metadata:
      labels:
        k8s-app: '$(appName)'
    spec:
      containers:
        - name: '$(appName)'
          image: $(containerRegistry)/$(appName):$(tag)
          ports:
            - containerPort: 3000
          env:
            - name: VAR_NEXT_PUBLIC_DATADOG_APPLICATION_ID
              value: c95bb572-c8e8-4296-abcd-a00290e95206
            - name: VAR_NEXT_PUBLIC_DATADOG_CLIENT_TOKEN
              value: pubd74e1e6ec7495c505076d831d1df3487
            - name: VAR_NEXT_PUBLIC_GA_ID
              value: '$(publicGaId)'
            - name: VAR_AZURE_AD_CLIENT_ID
              value: ad-client-id@azurekeyvault
            - name: VAR_AZURE_AD_CLIENT_SECRET
              value: ad-client-secret@azurekeyvault
            - name: VAR_NEXT_PUBLIC_AZURE_AD_TENANT_ID
              value: ad-tenant-id@azurekeyvault
            - name: VAR_NEXTAUTH_SECRET
              value: ad-auth-secret@azurekeyvault
            - name: VAR_NEXTAUTH_URL
              value: '$(appUrl)$(basePath)/api/auth'
            - name: VAR_PP_AUTH_LIB_AZURE_REDIRECT_URI
              value: '$(appUrl)$(basePath)/api/auth/callback/azure-ad'
            - name: VAR_NEXT_PUBLIC_NORTHSTAR_AUTH_API_URL
              value: '/api/auth'
            - name: VAR_NEXT_PUBLIC_BASE_PATH
              value: '$(basePath)'
            - name: VAR_NEXT_PUBLIC_NEXT_API_URL
              value: '/api'
            - name: VAR_NEXT_PUBLIC_APP_URL
              value: '$(appUrl)'
            - name: VAR_NEXT_PUBLIC_API_URL
              value: '/api/kpi-catalog/v2'
            - name: VAR_NEXTAUTH_SESSION_EXP_IN_SECONDS
              value: '$(nextAuthSessionExpInSeconds)'
            - name: VAR_NEXTAUTH_SESSION_EXP_IN_SECONDS
              value: '$(nextAuthSessionExpInSeconds)'
            - name: VAR_NEXT_PUBLIC_PEOPLE_PLATFORM_API_URL
              value: '/api/people-platform'
            - name: VAR_NEXT_PUBLIC_AUTH_API_URL
              value: '/api/auth-people-platform'
            - name: VAR_NEXT_PUBLIC_AUTH_API_URL_INTERNAL
              value: 'http://auth-api/api/auth-people-platform'
            - name: VAR_NEXT_PUBLIC_PEOPLE_PLATFORM_PHOTO_API_URL
              value: '/api/people-platform/photo'
            - name: VAR_NEXT_PUBLIC_LCM_URL
              value: '$(oprUrl)/api/lcm'
            - name: VAR_NEXT_PUBLIC_MY_TALENT_CARD_URL
              value: '$(oprUrl)/talent-card/me'
            - name: VAR_NEXT_PUBLIC_OPR_HOME_URL
              value: '$(oprUrl)/'
            - name: VAR_NEXT_PUBLIC_WORKDAY_URL
              value: 'https://www.myworkday.com/abinbev/d/.htmld'
            - name: VAR_NEXT_PUBLIC_MY_COMPENSATION_URL
              value: '$(rewardsUrl)'
            - name: VAR_NEXT_PUBLIC_CHEERSHUB_URL
              value: '$(cheershubUrl)'
            - name: VAR_NEXT_PUBLIC_ENV
              value: '$(environmentName)'
            - name: VAR_NODE_TLS_REJECT_UNAUTHORIZED
              value: '0'
            - name: VAR_NEXT_PUBLIC_WORKDAY_LINK_WORK_HISTORY
              value: 'https://www.myworkday.com/abinbev/d/task/2998\$7002.htmld'
            - name: VAR_PP_AUTH_LIB_REDIS_URL
              value: 'redis:6379'
            - name: VAR_PP_AUTH_LIB_AZURE_CLIENT_ID
              value: ad-client-id@azurekeyvault
            - name: VAR_PP_AUTH_LIB_AZURE_CLIENT_SECRET
              value: ad-client-secret@azurekeyvault
            - name: VAR_NEXT_PUBLIC_PP_AUTH_LIB_AZURE_TENANT_ID
              value: ad-tenant-id@azurekeyvault
            - name: VAR_PP_AUTH_LIB_AZURE_DEBUG
              value: 'true'
            - name: VAR_PP_AUTH_LIB_AZURE_HTTP_TIMEOUT
              value: '100000'
            - name: VAR_AUTH_API_URL_INTERNAL
              value: '$(appUrl)/api/auth-people-platform'
            - name: VAR_PP_AUTH_LIB_SYSTEM_ENUM
              value: 'northstar'
            - name: VAR_NEXT_PUBLIC_TOLGEE_CONTENT_DELIVERY_URL
              value: 'https://cdn.tolg.ee/315781bbc18e2bbb16e491e3107f53be'
            - name: VAR_NEXT_PUBLIC_TOLGEE_API_URL
              value: 'https://app.tolgee.io'
            - name: VAR_NEXT_PUBLIC_TOLGEE_API_KEY
              value: 'tgpak_ge2tcojzl42to4dsgr3hg3dkgf3dc3lbgnzwmnzqnjtgqyrrna3a'
            - name: VAR_NEXT_PUBLIC_NORTHSTAR_USER_API_URL
              value: '/api/user/v1/user'
            - name: NEXT_PUBLIC_DATADOG_APPLICATION_ID
              value: c95bb572-c8e8-4296-abcd-a00290e95206
            - name: NEXT_PUBLIC_DATADOG_CLIENT_TOKEN
              value: pubd74e1e6ec7495c505076d831d1df3487
            - name: NEXT_PUBLIC_GA_ID
              value: '$(publicGaId)'
            - name: AZURE_AD_CLIENT_ID
              value: ad-client-id@azurekeyvault
            - name: AZURE_AD_CLIENT_SECRET
              value: ad-client-secret@azurekeyvault
            - name: NEXT_PUBLIC_AZURE_AD_TENANT_ID
              value: ad-tenant-id@azurekeyvault
            - name: NEXTAUTH_SECRET
              value: ad-auth-secret@azurekeyvault
            - name: NEXTAUTH_URL
              value: '$(appUrl)$(basePath)/api/auth'
            - name: PP_AUTH_LIB_AZURE_REDIRECT_URI
              value: '$(appUrl)$(basePath)/api/auth/callback/azure-ad'
            - name: NEXT_PUBLIC_NORTHSTAR_AUTH_API_URL
              value: '/api/auth'
            - name: NEXT_PUBLIC_BASE_PATH
              value: '$(basePath)'
            - name: NEXT_PUBLIC_APP_URL
              value: '$(appUrl)'
            - name: NEXT_PUBLIC_API_URL
              value: '/api/kpi-catalog/v2'
            - name: NEXTAUTH_SESSION_EXP_IN_SECONDS
              value: '$(nextAuthSessionExpInSeconds)'
            - name: NEXTAUTH_SESSION_EXP_IN_SECONDS
              value: '$(nextAuthSessionExpInSeconds)'
            - name: NEXT_PUBLIC_PEOPLE_PLATFORM_API_URL
              value: '/api/people-platform'
            - name: NEXT_PUBLIC_AUTH_API_URL
              value: '/api/auth-people-platform'
            - name: NEXT_PUBLIC_AUTH_API_URL_INTERNAL
              value: 'http://auth-api/api/auth-people-platform'
            - name: NEXT_PUBLIC_PEOPLE_PLATFORM_PHOTO_API_URL
              value: '/api/people-platform/photo'
            - name: NEXT_PUBLIC_LCM_URL
              value: '$(oprUrl)/api/lcm'
            - name: NEXT_PUBLIC_MY_TALENT_CARD_URL
              value: '$(oprUrl)/talent-card/me'
            - name: NEXT_PUBLIC_OPR_HOME_URL
              value: '$(oprUrl)/'
            - name: NEXT_PUBLIC_WORKDAY_URL
              value: 'https://www.myworkday.com/abinbev/d/.htmld'
            - name: NEXT_PUBLIC_MY_COMPENSATION_URL
              value: '$(rewardsUrl)'
            - name: NEXT_PUBLIC_CHEERSHUB_URL
              value: '$(cheershubUrl)'
            - name: NEXT_PUBLIC_ENV
              value: '$(environmentName)'
            - name: NODE_TLS_REJECT_UNAUTHORIZED
              value: '0'
            - name: NEXT_PUBLIC_WORKDAY_LINK_WORK_HISTORY
              value: 'https://www.myworkday.com/abinbev/d/task/2998\$7002.htmld'
            - name: PP_AUTH_LIB_REDIS_URL
              value: 'redis:6379'
            - name: PP_AUTH_LIB_AZURE_CLIENT_ID
              value: ad-client-id@azurekeyvault
            - name: PP_AUTH_LIB_AZURE_CLIENT_SECRET
              value: ad-client-secret@azurekeyvault
            - name: NEXT_PUBLIC_PP_AUTH_LIB_AZURE_TENANT_ID
              value: ad-tenant-id@azurekeyvault
            - name: PP_AUTH_LIB_AZURE_DEBUG
              value: 'true'
            - name: PP_AUTH_LIB_AZURE_HTTP_TIMEOUT
              value: '100000'
            - name: AUTH_API_URL_INTERNAL
              value: '$(appUrl)/api/auth-people-platform'
            - name: PP_AUTH_LIB_SYSTEM_ENUM
              value: 'northstar'
            - name: NEXT_PUBLIC_TOLGEE_CONTENT_DELIVERY_URL
              value: 'https://cdn.tolg.ee/315781bbc18e2bbb16e491e3107f53be'
            - name: NEXT_PUBLIC_TOLGEE_API_URL
              value: 'https://app.tolgee.io'
            - name: NEXT_PUBLIC_TOLGEE_API_KEY
              value: 'tgpak_ge2tcojzl42to4dsgr3hg3dkgf3dc3lbgnzwmnzqnjtgqyrrna3a'
          resources: {}
          livenessProbe:
            httpGet:
              path: /catalog/new-kpi-catalog
              port: 3000
              scheme: HTTP
            initialDelaySeconds: 30
            timeoutSeconds: 1
            periodSeconds: 30
            successThreshold: 1
            failureThreshold: 5
          readinessProbe:
            httpGet:
              path: /catalog/new-kpi-catalog
              port: 3000
              scheme: HTTP
            initialDelaySeconds: 30
            timeoutSeconds: 10
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 5
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      securityContext: {}
      imagePullSecrets:
        - name: acr-registry
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
